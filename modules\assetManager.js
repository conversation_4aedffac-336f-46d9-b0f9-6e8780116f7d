// AssetManager Module - Handles loading and managing game assets
class AssetManager {
    constructor() {
        this.assets = new Map();
        this.loadingPromises = new Map();
        this.isLoading = false;
    }

    async preloadAssets(assetList) {
        this.isLoading = true;
        
        try {
            const loadPromises = assetList.map(async (asset) => {
                if (typeof asset === 'string') {
                    return await PIXI.Assets.load(asset);
                } else if (asset.url && asset.name) {
                    const texture = await PIXI.Assets.load(asset.url);
                    this.assets.set(asset.name, texture);
                    return texture;
                }
            });

            await Promise.all(loadPromises);
            console.log('All assets loaded successfully');
        } catch (error) {
            console.warn('Some assets failed to load:', error);
        } finally {
            this.isLoading = false;
        }
    }

    getAsset(name) {
        return this.assets.get(name) || PIXI.Texture.from(name);
    }

    hasAsset(name) {
        return this.assets.has(name);
    }

    isLoadingAssets() {
        return this.isLoading;
    }

    createSprite(assetName) {
        const texture = this.getAsset(assetName);
        if (texture) {
            return new PIXI.Sprite(texture);
        }
        
        // Return placeholder if asset not found
        return GameUtils.createImageSprite(assetName);
    }

    getLoadingProgress() {
        // This is a simplified version - PIXI.Assets has more sophisticated progress tracking
        return this.isLoading ? 0.5 : 1;
    }

    async loadSingleAsset(url, name = null) {
        if (this.loadingPromises.has(url)) {
            return this.loadingPromises.get(url);
        }

        const promise = PIXI.Assets.load(url);
        this.loadingPromises.set(url, promise);

        try {
            const texture = await promise;
            if (name) {
                this.assets.set(name, texture);
            }
            return texture;
        } catch (error) {
            console.warn(`Failed to load asset: ${url}`, error);
            return null;
        } finally {
            this.loadingPromises.delete(url);
        }
    }

    clearAssets() {
        this.assets.clear();
        this.loadingPromises.clear();
    }

    getAssetInfo() {
        return {
            loadedAssets: this.assets.size,
            isLoading: this.isLoading,
            pendingLoads: this.loadingPromises.size
        };
    }
}

// Puzzle parts configuration
const PUZZLE_PARTS = [
    { name: 'Face', textureUrl: 'assets/images/chiikawa_face.png' },
    { name: 'Left Ear', textureUrl: 'assets/images/chiikawa_left_ear.png' },
    { name: 'Right Ear', textureUrl: 'assets/images/chiikawa_right_ear.png' },
    { name: 'Left Eye', textureUrl: 'assets/images/chiikawa_left_eye.png' },
    { name: 'Right Eye', textureUrl: 'assets/images/chiikawa_right_eye.png' },
    { name: 'Left Eyebrow', textureUrl: 'assets/images/chiikawa_left_eyebrow.png' },
    { name: 'Right Eyebrow', textureUrl: 'assets/images/chiikawa_right_eyebrow.png' },
    { name: 'Nose', textureUrl: 'assets/images/chiikawa_nose.png' },
    { name: 'Mouth', textureUrl: 'assets/images/chiikawa_mouth.png' },
    { name: 'Left Cheek', textureUrl: 'assets/images/chiikawa_left_cheek.png' },
    { name: 'Right Cheek', textureUrl: 'assets/images/chiikawa_right_cheek.png' },
    { name: 'Body', textureUrl: 'assets/images/chiikawa_body_new.png' },
    { name: 'Left Hand', textureUrl: 'assets/images/chiikawa_left_hand.png' },
    { name: 'Right Hand', textureUrl: 'assets/images/chiikawa_right_hand.png' },
    { name: 'Left Leg', textureUrl: 'assets/images/chiikawa_left_leg.png' },
    { name: 'Right Leg', textureUrl: 'assets/images/chiikawa_right_leg.png' }
];

window.AssetManager = AssetManager;
window.PUZZLE_PARTS = PUZZLE_PARTS;