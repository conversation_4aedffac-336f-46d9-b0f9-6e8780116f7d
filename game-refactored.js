// Main Game Module - Simplified and modular
class ChiikawaGame {
    constructor() {
        this.app = null;
        this.gameState = new GameStateManager();
        this.ui = null;
        this.assetManager = new AssetManager();
        this.languageManager = null;
        this.puzzleContainer = new PIXI.Container();
        this.cursorSprite = null;
        
        // Bound methods to avoid context issues
        this.boundOnPointerMove = (event) => this.onPointerMove(event);
        this.boundOnCanvasClick = (event) => this.onCanvasClick(event);
        
        // UI elements
        this.loadingText = null;
        this.titleText = null;
        this.startButton = null;
        this.startButtonText = null; // Add this line for the start button text
        this.memoryImage = null;
        this.timerText = null;
        this.instructionText = null;
        this.playerCreationText = null;
        this.playAgainButton = null;
        this.shareButton = null;
        this.playAgainTooltip = null; // Add tooltip references
        this.shareTooltip = null;
    }

    async init() {
        // Initialize PixiJS application
        this.app = new PIXI.Application({
            width: 600,
            height: 600,
            backgroundColor: 0xFFFFFF,
            antialias: true,
            autoDensity: true,
            resolution: window.devicePixelRatio || 1
        });

        document.getElementById('game-container').appendChild(this.app.view);

        // Initialize modules
        this.ui = new UIManager(this.app);
        this.app.stage.addChild(this.puzzleContainer);

        // Set background
        this.setBackground();

        this.app.view.style.cursor = 'default';
        
        // Always track mouse position
        this.app.view.addEventListener('pointermove', this.boundOnPointerMove);

        // Initialize language system
        await this.initializeLanguageSystem();
        
        // Set up state callbacks
        this.setupStateCallbacks();

        // Start the game
        this.showLoadingScreen();
        await this.loadAssets();
        this.gameState.setState('START_SCREEN');
    }

    async initializeLanguageSystem() {
        // Wait for translations to be available
        if (window.websiteLanguageManager) {
            this.languageManager = window.websiteLanguageManager;
        } else {
            // Fallback: wait for website language manager to be available
            await new Promise(resolve => {
                const checkManager = () => {
                    if (window.websiteLanguageManager) {
                        this.languageManager = window.websiteLanguageManager;
                        resolve();
                    } else {
                        setTimeout(checkManager, 100);
                    }
                };
                checkManager();
            });
        }
    }
    
    updateUILanguage() {
        if (!this.languageManager) return;
        
        // Update UI based on current game state
        const currentState = this.gameState.getState();
        if (currentState === 'START_SCREEN') {
            this.showStartScreen();
        } else if (currentState === 'MEMORY') {
            // Update timer text if in memory phase
            if (this.timerText) {
                const timeLeft = this.gameState.getMemoryTimer();
                this.timerText.text = `${this.languageManager.getText('game.memorizeThis')} ${timeLeft}`;
            }
        } else if (currentState === 'PUZZLING') {
            this.updateInstructionText();
        } else if (currentState === 'REVEAL') {
            if (this.playerCreationText) {
                this.playerCreationText.text = this.languageManager.getText('game.yourMasterpiece');
            }
        }
        
        // Update HTML content
        this.updateHTMLLanguage();
    }
    
    updateHTMLLanguage() {
        if (!this.languageManager) return;
        
        // Update document title
        document.title = this.languageManager.getText('website.title');
        
        // Update meta description
        const metaDescription = document.querySelector('meta[name="description"]');
        if (metaDescription) {
            metaDescription.content = this.languageManager.getText('website.description');
        }
        
        // Update meta keywords
        const metaKeywords = document.querySelector('meta[name="keywords"]');
        if (metaKeywords) {
            metaKeywords.content = this.languageManager.getText('website.keywords');
        }
    }

    setupStateCallbacks() {
        this.gameState.onStateChange('START_SCREEN', () => this.showStartScreen());
        this.gameState.onStateChange('MEMORY', () => this.showMemoryPhase());
        this.gameState.onStateChange('PUZZLING', () => this.startPuzzlingPhase());
        this.gameState.onStateChange('REVEAL', () => this.showRevealPhase());
    }

    setBackground() {
        // Create a solid color background instead of image
        const background = new PIXI.Graphics();
        background.beginFill(0xd5ecbf); // Use the specified color
        background.drawRect(0, 0, this.app.screen.width, this.app.screen.height);
        background.endFill();
        // Add to the bottom of the stage (behind everything else)
        this.app.stage.addChildAt(background, 0);
    }

    showLoadingScreen() {
        this.loadingText = this.ui.createText(this.languageManager.getText('game.loading'), { fontSize: 24 });
        this.loadingText.anchor.set(0.5);
        this.loadingText.x = this.app.screen.width / 2;
        this.loadingText.y = this.app.screen.height / 2;
        this.ui.addToUI(this.loadingText);
    }

    async loadAssets() {
        const assetsToLoad = [
            'assets/images/chiikawa_full.png',
            ...PUZZLE_PARTS.map(part => part.textureUrl)
        ];

        await this.assetManager.preloadAssets(assetsToLoad);
    }

    showStartScreen() {
        this.ui.clearUI();

        this.titleText = this.ui.createText(this.languageManager.getText('game.title'), {
            fontSize: 48,
            fontWeight: 'bold'
        });
        this.titleText.anchor.set(0.5);
        this.titleText.x = this.app.screen.width / 2;
        this.titleText.y = this.app.screen.height / 2 - 100;
        this.ui.addToUI(this.titleText);

        this.startButton = this.ui.createIconButtonWithImage(
            'start', 
            this.app.screen.width / 2, 
            this.app.screen.height / 2 + 50, 
            () => this.gameState.setState('MEMORY')
        );
        this.ui.addToUI(this.startButton);
        
        // Add text below the start button
        this.startButtonText = this.ui.createText(this.languageManager.getText('game.pressToStart'), {
            fontSize: 56, // Double the font size (28 -> 56)
            fontWeight: 'bold', // Reduce font weight (bolder -> bold)
            fill: 0xD2B48C, // Lighter brown color (two shades lighter)
            stroke: 0x5D2906, // Stroke color unchanged
            strokeThickness: 3,
            fontFamily: 'Fredoka One, cursive' // Google Fonts font
        });
        this.startButtonText.anchor.set(0.5);
        this.startButtonText.x = this.app.screen.width / 2;
        this.startButtonText.y = this.app.screen.height / 2 + 200;
        this.ui.addToUI(this.startButtonText);
    }

    showMemoryPhase() {
        this.ui.clearUI();
        this.gameState.resetPuzzleState();
        
        this.memoryImage = GameUtils.createImageSprite('assets/images/chiikawa_full.png');
        if (this.memoryImage) {
            this.memoryImage.anchor.set(0.5);
            this.memoryImage.x = this.app.screen.width / 2;
            this.memoryImage.y = this.app.screen.height / 2;
            // Use original size without scaling, just center it
            this.ui.addToUI(this.memoryImage);
        }
        
        this.timerText = this.ui.createText(`${this.languageManager.getText('game.memorizeThis')} ${this.gameState.getMemoryTimer()}`, {
            fontSize: 32,
            fontWeight: 'bold'
        });
        this.timerText.anchor.set(0.5);
        this.timerText.x = this.app.screen.width / 2;
        this.timerText.y = 100;
        this.ui.addToUI(this.timerText);
        
        this.gameState.startMemoryTimer((timeLeft) => {
            this.timerText.text = `${this.languageManager.getText('game.memorizeThis')} ${timeLeft}`;
            if (timeLeft <= 0) {
                this.fadeOutMemoryPhase();
            }
        });
    }

    fadeOutMemoryPhase() {
        this.ui.fadeOutElement(this.memoryImage, 0.1);
        this.ui.fadeOutElement(this.timerText, 0.1, () => {
            this.gameState.setState('PUZZLING');
        });
    }

    startPuzzlingPhase() {
        this.ui.clearUI();
        
        this.app.view.style.cursor = 'default';
        
        this.updateInstructionText();
        this.setupCursorSprite();
        
        this.app.view.removeEventListener('click', this.boundOnCanvasClick);
        this.app.view.addEventListener('click', this.boundOnCanvasClick);
    }

    updateInstructionText() {
        if (this.instructionText) {
            this.ui.removeFromUI(this.instructionText);
        }
        
        const currentPart = PUZZLE_PARTS[this.gameState.getCurrentPartIndex()];
        const partName = this.languageManager.getText(`game.parts.${currentPart.name}`) || currentPart.name;
        this.instructionText = this.ui.createText(`${this.languageManager.getText('game.placeInstruction')} ${partName}`, { fontSize: 24 });
        this.instructionText.anchor.set(0.5);
        this.instructionText.x = this.app.screen.width / 2;
        this.instructionText.y = 50;
        this.ui.addToUI(this.instructionText);
    }

    setupCursorSprite() {
        if (this.cursorSprite) {
            this.ui.removeFromUI(this.cursorSprite);
        }
        
        const currentPart = PUZZLE_PARTS[this.gameState.getCurrentPartIndex()];
        this.cursorSprite = GameUtils.createImageSprite(currentPart.textureUrl);
        
        if (this.cursorSprite) {
            this.cursorSprite.anchor.set(0.5);
            const mousePos = this.gameState.getMousePosition();
            this.cursorSprite.x = mousePos.x || this.app.screen.width / 2;
            this.cursorSprite.y = mousePos.y || this.app.screen.height / 2;
            // Use original size but scale to 50% for better visual quality
            this.cursorSprite.scale.set(0.5);
            this.ui.addToUI(this.cursorSprite);
            

            this.cursorSprite.alpha = 1;
        }
    }

    onPointerMove(event) {
        const mousePos = GameUtils.getMousePosition(
            event, 
            this.app.view, 
            this.app.screen.width, 
            this.app.screen.height
        );
        
        this.gameState.updateMousePosition(mousePos.x, mousePos.y);
        
        if (this.gameState.isState('PUZZLING') && this.cursorSprite) {
            this.cursorSprite.x = mousePos.x;
            this.cursorSprite.y = mousePos.y;
        }
    }

    onCanvasClick(event) {
        if (!this.gameState.isState('PUZZLING') || !this.cursorSprite) return;
        
        // Place the part
        const currentPart = PUZZLE_PARTS[this.gameState.getCurrentPartIndex()];
        const placedPart = GameUtils.createImageSprite(currentPart.textureUrl);
        
        if (placedPart) {
            placedPart.anchor.set(0.5);
            placedPart.x = this.cursorSprite.x;
            placedPart.y = this.cursorSprite.y;
            // Use original size but scale to 50% for better visual quality
            placedPart.scale.set(0.5);
            this.puzzleContainer.addChild(placedPart);
            
            // Immediately start fading out the placed part to increase game challenge
            this.fadeOutElement(placedPart, 0.05); // Fast fade out
        }
        
        this.gameState.nextPart();
        
        if (this.gameState.isLastPart(PUZZLE_PARTS.length)) {
            setTimeout(() => {
                this.gameState.setState('REVEAL');
            }, 1000);
        } else {
            this.updateInstructionText();
            this.setupCursorSprite();
        }
    }

    showRevealPhase() {
        this.ui.clearUI();
        
        this.app.view.style.cursor = 'default';
        this.app.view.removeEventListener('click', this.onCanvasClick);
        
        // Make all placed parts visible again
        this.puzzleContainer.children.forEach(child => {
            child.alpha = 1;
        });
        
        this.playerCreationText = this.ui.createText(this.languageManager.getText('game.yourMasterpiece'), {
            fontSize: 36,
            fontWeight: 'bold'
        });
        this.playerCreationText.anchor.set(0.5);
        this.playerCreationText.x = this.app.screen.width / 2;
        this.playerCreationText.y = 50;
        this.ui.addToUI(this.playerCreationText);
        
        this.playAgainButton = this.ui.createIconButton(
            'restart', 
            this.app.screen.width - 120, 
            this.app.screen.height - 60, 
            () => this.resetGame(),
            0.5 // Scale to 1/2 of original size
        );
        this.ui.addToUI(this.playAgainButton);
        
        // Add tooltip for play again button
        this.playAgainTooltip = this.ui.createTooltip('Play Again', this.app.screen.width - 120, this.app.screen.height - 60);
        this.ui.addToUI(this.playAgainTooltip);
        
        // Add hover events for tooltip
        this.playAgainButton.on('pointerover', () => {
            // Adjust position if tooltip would go off screen on the left
            const tooltipWidth = this.playAgainTooltip.background.width;
            if (this.playAgainTooltip.x - tooltipWidth/2 < 0) {
                this.playAgainTooltip.x = tooltipWidth/2;
            }
            this.ui.fadeInElement(this.playAgainTooltip, 0.1);
        });
        
        this.playAgainButton.on('pointerout', () => {
            this.ui.fadeOutElement(this.playAgainTooltip, 0.1);
        });

        this.shareButton = this.ui.createIconButton(
            'download', 
            this.app.screen.width - 40, 
            this.app.screen.height - 60, 
            () => this.shareCreation(),
            0.5 // Scale to 1/2 of original size
        );
        this.ui.addToUI(this.shareButton);
        
        // Add tooltip for share button (right-aligned)
        this.shareTooltip = this.ui.createTooltip('Save Image', this.app.screen.width - 40, this.app.screen.height - 60);
        this.ui.addToUI(this.shareTooltip);
        
        // Add hover events for tooltip
        this.shareButton.on('pointerover', () => {
            // Adjust position if tooltip would go off screen on the right
            const tooltipWidth = this.shareTooltip.background.width;
            if (this.shareTooltip.x + tooltipWidth/2 > this.app.screen.width) {
                // Move tooltip so its right edge aligns with the right edge of the screen
                this.shareTooltip.x = this.app.screen.width - tooltipWidth/2;
            }
            this.ui.fadeInElement(this.shareTooltip, 0.1);
        });
        
        this.shareButton.on('pointerout', () => {
            this.ui.fadeOutElement(this.shareTooltip, 0.1);
        });
        
        this.animateRevealElements();
    }

    animateRevealElements() {
        this.ui.fadeInElement(this.playerCreationText);
        this.ui.fadeInElement(this.playAgainButton);
        this.ui.fadeInElement(this.shareButton);
    }

    fadeOutElement(element, speed = 0.1, callback = null) {
        const fadeOut = () => {
            element.alpha -= speed;
            if (element.alpha <= 0) {
                element.alpha = 0;
                if (callback) callback();
            } else {
                requestAnimationFrame(fadeOut);
            }
        };
        fadeOut();
    }

    resetGame() {
        this.puzzleContainer.removeChildren();
        this.ui.clearUI();
        
        this.app.view.style.cursor = 'default';
        this.app.view.removeEventListener('click', this.onCanvasClick);
        
        this.gameState.resetPuzzleState();
        this.gameState.setState('START_SCREEN');
    }

    shareCreation() {
        try {
            // Create a temporary container for export
            const exportContainer = new PIXI.Container();
            
            // Add solid color background instead of image
            const background = new PIXI.Graphics();
            background.beginFill(0xd5ecbf); // Use the specified color
            background.drawRect(0, 0, this.app.screen.width, this.app.screen.height);
            background.endFill();
            exportContainer.addChild(background);
            
            // Copy all puzzle pieces to the export container
            this.puzzleContainer.children.forEach(child => {
                const copy = GameUtils.copySprite(child);
                if (copy) {
                    exportContainer.addChild(copy);
                }
            });
            
            // Add watermark text at the bottom
            const watermarkText = new PIXI.Text('Generated BY: https://chiikawapuzzle.net', {
                fontFamily: 'Arial',
                fontSize: 16,
                fill: 0x888888,
                align: 'center',
                fontWeight: 'bold'
            });
            watermarkText.anchor.set(0.5, 0);
            watermarkText.x = this.app.screen.width / 2;
            watermarkText.y = this.app.screen.height - 30;
            exportContainer.addChild(watermarkText);
            
            // Extract the export container
            const canvas = this.app.renderer.extract.canvas(exportContainer);
            
            GameUtils.downloadCanvas(canvas, 'chiikawa-creation.png');
            
            // Clean up
            exportContainer.destroy({children: true});
        } catch (error) {
            console.error('Failed to share creation:', error);
            alert('Failed to save image. Please try again.');
        }
    }
}

// Initialize the game when page loads
window.addEventListener('load', async () => {
    const game = new ChiikawaGame();
    await game.init();
});