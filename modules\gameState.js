// GameState Module - Manages game states and transitions
class GameStateManager {
    constructor() {
        this.state = 'LOADING';
        this.currentPartIndex = 0;
        this.memoryTimer = 5;
        this.timerInterval = null;
        this.currentMouseX = 0;
        this.currentMouseY = 0;
        this.callbacks = {};
    }

    setState(newState) {
        const oldState = this.state;
        this.state = newState;
        console.log(`State changed: ${oldState} -> ${newState}`);
        
        if (this.callbacks[newState]) {
            this.callbacks[newState]();
        }
    }

    getState() {
        return this.state;
    }

    onStateChange(state, callback) {
        this.callbacks[state] = callback;
    }

    resetPuzzleState() {
        this.currentPartIndex = 0;
        this.memoryTimer = 5;
        this.clearTimer();
    }

    nextPart() {
        this.currentPartIndex++;
        return this.currentPartIndex;
    }

    getCurrentPartIndex() {
        return this.currentPartIndex;
    }

    isLastPart(totalParts) {
        return this.currentPartIndex >= totalParts;
    }

    updateMousePosition(x, y) {
        this.currentMouseX = x;
        this.currentMouseY = y;
    }

    getMousePosition() {
        return { x: this.currentMouseX, y: this.currentMouseY };
    }

    startMemoryTimer(callback) {
        this.memoryTimer = 5;
        this.timerInterval = setInterval(() => {
            this.memoryTimer--;
            if (callback) callback(this.memoryTimer);
            
            if (this.memoryTimer <= 0) {
                this.clearTimer();
            }
        }, 1000);
    }

    getMemoryTimer() {
        return this.memoryTimer;
    }

    clearTimer() {
        if (this.timerInterval) {
            clearInterval(this.timerInterval);
            this.timerInterval = null;
        }
    }

    isState(state) {
        return this.state === state;
    }
}

window.GameStateManager = GameStateManager;