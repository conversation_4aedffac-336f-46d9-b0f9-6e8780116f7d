<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chiikawa Puzzle - The Ultimate Memory Challenge Game | Play Free Online</title>
    <meta name="description" content="Experience the adorable Chiikawa Puzzle game! Test your memory skills with this charming puzzle challenge featuring the beloved Japanese character. Play free online now!">
    <meta name="keywords" content="chiikawa puzzle, memory game, puzzle game, chiikawa character, japanese puzzle, online game, free puzzle, memory challenge, brain training">
    <meta name="robots" content="index, follow">
    <meta property="og:title" content="Chiikawa Puzzle - The Ultimate Memory Challenge Game">
    <meta property="og:description" content="Test your memory with the adorable <PERSON><PERSON><PERSON> character in this engaging puzzle game. Play free online!">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://chiikawapuzzle.net">
    <link rel="canonical" href="https://chiikawapuzzle.net">
    <link href="https://fonts.googleapis.com/css2?family=Fredoka+One:wght@400;600;700&family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="style.css">
    <style>
        /* Benefits & Features Section */
        #benefits-features {
            background: linear-gradient(135deg, #a8e6cf 0%, #7fcdcd 100%);
            color: white;
            padding: 4rem 0;
        }

        .benefits-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-top: 2rem;
        }

        .benefit-item {
            background: rgba(255,255,255,0.1);
            padding: 2rem;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
        }

        .benefit-item h3 {
            color: #ffd700;
            margin-bottom: 1rem;
            font-size: 1.2rem;
        }

        /* SEO Content Section */
        #seo-content {
            background: #f8f9fa;
            padding: 4rem 0;
        }

        .seo-content {
            max-width: 800px;
            margin: 0 auto;
            line-height: 1.8;
        }

        .seo-content p {
            margin-bottom: 1.5rem;
            text-align: justify;
        }

        /* FAQ Section */
        #faq {
            background: linear-gradient(135deg, #a8e6cf 0%, #7fcdcd 100%);
            color: white;
            padding: 4rem 0;
        }

        .faq-content {
            max-width: 900px;
            margin: 2rem auto 0;
        }

        .faq-item {
            background: rgba(255,255,255,0.1);
            margin-bottom: 1.5rem;
            border-radius: 15px;
            overflow: hidden;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .faq-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }

        .faq-item h3 {
            background: rgba(255,255,255,0.2);
            padding: 1.5rem 2rem;
            margin: 0;
            font-size: 1.1rem;
            font-weight: 600;
            color: #ffd700;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }

        .faq-item p {
            padding: 1.5rem 2rem;
            margin: 0;
            line-height: 1.6;
            font-size: 0.95rem;
        }

        .faq-item strong {
            color: #ffd700;
            font-weight: 600;
        }

        .faq-item em {
            color: #ffeb3b;
            font-style: normal;
            font-weight: 500;
        }

        @media (max-width: 768px) {
            .faq-item h3 {
                padding: 1rem 1.5rem;
                font-size: 1rem;
            }
            
            .faq-item p {
                padding: 1rem 1.5rem;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header Section -->
    <header>
        <div class="container">
            <div class="header-content">
                <div class="logo-container">
                    <img src="assets/icon/favicon.ico" alt="Chiikawa Puzzle Logo" class="logo">
                    <h1>Chiikawa Puzzle</h1>
                </div>
                <div class="header-controls">
                    <button class="mobile-menu-toggle" aria-label="Toggle mobile menu">
                        <span></span>
                        <span></span>
                        <span></span>
                    </button>
                </div>
            </div>
            <nav class="nav-menu">
                <ul>
                    <li><a href="#game">Game</a></li>
                    <li><a href="#how-to-play">How to Play</a></li>
                    <li><a href="#features">Features</a></li>
                    <li><a href="#faq">FAQ</a></li>
                    <li class="nav-language-item">
                        <div class="language-selector" id="languageSelector">
                            <button class="language-select-button" id="languageButton" aria-label="选择语言 / Select Language / 言語を選択" aria-expanded="false" aria-haspopup="listbox">
                                <span class="language-flag">🇨🇳</span>
                                <span class="language-text">中文</span>
                                <span class="language-arrow">▼</span>
                            </button>
                            <ul class="language-dropdown" id="languageDropdown" role="listbox" aria-label="Language options">
                                <li class="language-option" data-value="zh" role="option" aria-selected="true">
                                    <span class="language-flag">🇨🇳</span>
                                    <span class="language-text">中文</span>
                                </li>
                                <li class="language-option" data-value="ja" role="option" aria-selected="false">
                                    <span class="language-flag">🇯🇵</span>
                                    <span class="language-text">日本語</span>
                                </li>
                                <li class="language-option" data-value="en" role="option" aria-selected="false">
                                    <span class="language-flag">🇺🇸</span>
                                    <span class="language-text">English</span>
                                </li>
                                <li class="language-option" data-value="ko" role="option" aria-selected="false">
                                    <span class="language-flag">🇰🇷</span>
                                    <span class="language-text">한국어</span>
                                </li>
                                <li class="language-option" data-value="fr" role="option" aria-selected="false">
                                    <span class="language-flag">🇫🇷</span>
                                    <span class="language-text">Français</span>
                                </li>
                                <li class="language-option" data-value="de" role="option" aria-selected="false">
                                    <span class="language-flag">🇩🇪</span>
                                    <span class="language-text">Deutsch</span>
                                </li>
                                <li class="language-option" data-value="es" role="option" aria-selected="false">
                                    <span class="language-flag">🇪🇸</span>
                                    <span class="language-text">Español</span>
                                </li>
                                <li class="language-option" data-value="it" role="option" aria-selected="false">
                                    <span class="language-flag">🇮🇹</span>
                                    <span class="language-text">Italiano</span>
                                </li>
                                <li class="language-option" data-value="ru" role="option" aria-selected="false">
                                    <span class="language-flag">🇷🇺</span>
                                    <span class="language-text">Русский</span>
                                </li>
                                <li class="language-option" data-value="pt" role="option" aria-selected="false">
                                    <span class="language-flag">🇵🇹</span>
                                    <span class="language-text">Português</span>
                                </li>
                                <li class="language-option" data-value="th" role="option" aria-selected="false">
                                    <span class="language-flag">🇹🇭</span>
                                    <span class="language-text">ภาษาไทย</span>
                                </li>
                            </ul>
                            <!-- Hidden select for form compatibility -->
                            <select id="languageSelect" class="language-select-hidden" aria-hidden="true" tabindex="-1">
                                <option value="zh">中文</option>
                                <option value="ja">日本語</option>
                                <option value="en">English</option>
                                <option value="ko">한국어</option>
                                <option value="fr">Français</option>
                                <option value="de">Deutsch</option>
                                <option value="es">Español</option>
                                <option value="it">Italiano</option>
                                <option value="ru">Русский</option>
                                <option value="pt">Português</option>
                                <option value="th">ภาษาไทย</option>
                            </select>
                        </div>
                    </li>
                </ul>
            </nav>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="hero">
        <div class="container">
            <h2>Chiikawa Puzzle - Adorable Memory Challenge</h2>
            <p>Test your memory skills with the irresistibly cute Chiikawa character! Memorize the complete image in just 5 seconds, then reconstruct this adorable little character using puzzle pieces. It's not just a puzzle game—it's a delightful brain training adventure that combines fun with cognitive enhancement!</p>
            <a href="#game-container" class="cta-button">Start Chiikawa Puzzle Challenge</a>
        </div>
    </section>

    <!-- Main Game Section -->
    <main>
        <div id="game-container">
        </div>
        
        <section id="game-intro">
            <div class="container">
                <h2>Master the Game</h2>
                <p>Follow these simple steps to become a puzzle master!</p>
            </div>
        </section>

        <!-- Game Information Section -->
        <section id="game-info">
            <div class="container">
                <h2>About Chiikawa Puzzle - The Ultimate Memory Game</h2>
                <div class="info-content">
                    <p><strong>Chiikawa Puzzle</strong> is an innovative memory-based puzzle game that revolutionizes traditional puzzle gaming by combining the irresistible charm of the beloved Japanese character Chiikawa (ちいかわ) with advanced cognitive training mechanics. This unique browser-based game offers players an unprecedented opportunity to enhance their memory skills while enjoying one of Japan's most adorable characters.</p>
                    
                    <p>What sets our <em>Chiikawa Puzzle</em> apart is its scientifically-designed gameplay that targets working memory, visual-spatial intelligence, and attention span. Players must memorize the complete Chiikawa character during a brief 5-second viewing period, then reconstruct the image using scattered puzzle pieces - a challenge that exercises both short-term memory and spatial reasoning abilities.</p>
                    
                    <p>The game leverages the psychological benefits of interacting with cute characters (known as "kawaii therapy" in Japan) while providing substantial cognitive training. This perfect balance of entertainment and brain exercise makes <strong>Chiikawa Puzzle</strong> suitable for players of all ages, from children developing their memory skills to adults seeking stress relief and mental stimulation.</p>
                    
                    <p>Built with cutting-edge web technologies, our <em>Chiikawa Puzzle</em> game runs smoothly on all devices - desktop computers, tablets, and smartphones. No downloads or installations required - simply open your browser and start training your brain with the most adorable puzzle game on the internet!</p>
                </div>
            </div>
        </section>

        <!-- How to Play Section -->
        <section id="how-to-play">
            <div class="container">
                <h2>How to Master Chiikawa Puzzle - Complete Gameplay Guide</h2>
                <div class="steps">
                    <div class="step">
                        <h3>🎯 Step 1: Memory Phase - Focus & Memorize</h3>
                        <p>Click the "Start Game" button to begin your <strong>Chiikawa Puzzle</strong> adventure. You'll see the complete, adorable Chiikawa character displayed for exactly 5 seconds. During this crucial memorization period, focus intensely on every detail - the position of the ears, eyes, cheeks, body, and limbs. This brief window is your only opportunity to study the target image, so make every second count for optimal puzzle-solving success!</p>
                    </div>
                    <div class="step">
                        <h3>🧩 Step 2: Reconstruction Challenge</h3>
                        <p>After the memory phase ends, the complete image disappears and you're presented with individual <em>Chiikawa puzzle pieces</em> scattered around the game area. Using only your memory of the original image, click to place each piece and recreate the beloved character as accurately as possible. This cognitive challenge exercises your visual memory, spatial reasoning, and attention to detail.</p>
                    </div>
                    <div class="step">
                        <h3>🎨 Step 3: Reveal & Share Your Creation</h3>
                        <p>Once you've placed all puzzle pieces, the game reveals your unique interpretation alongside the original Chiikawa character. Compare your memory-based recreation with the target image and share your charming creation with friends and family. Each <strong>Chiikawa Puzzle</strong> attempt creates a one-of-a-kind artwork that reflects your memory skills and creativity!</p>
                    </div>
                    <div class="step">
                        <h3>🏆 Step 4: Improve & Challenge Yourself</h3>
                        <p>The more you play <em>Chiikawa Puzzle</em>, the better your memory and spatial skills become. Challenge yourself to remember more details, place pieces more accurately, and reduce reconstruction time. This progressive improvement makes our puzzle game an excellent tool for ongoing cognitive development and brain training.</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Game Features Section -->
        <section id="features">
            <div class="container">
                <h2>Game Features</h2>
                <div class="features-grid">
                    <div class="feature">
                        <h3>Adorable Characters</h3>
                        <p>Featuring the irresistibly cute Chiikawa character in various puzzle configurations.</p>
                    </div>
                    <div class="feature">
                        <h3>Memory Challenge</h3>
                        <p>Test and improve your memory skills with this engaging puzzle experience.</p>
                    </div>
                    <div class="feature">
                        <h3>Shareable Results</h3>
                        <p>Easily share your puzzle creations with friends through our integrated sharing feature.</p>
                    </div>
                    <div class="feature">
                        <h3>Responsive Design</h3>
                        <p>Enjoy the game on any device with our fully responsive web design.</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Benefits & Features Section -->
        <section id="benefits-features">
            <div class="container">
                <h2>Why Choose Chiikawa Puzzle? Benefits & Features</h2>
                <div class="benefits-grid">
                    <div class="benefit-item">
                        <h3>🧠 Cognitive Enhancement</h3>
                        <p>Our <strong>Chiikawa Puzzle</strong> game scientifically improves working memory, visual processing, and spatial intelligence through engaging gameplay that feels more like entertainment than training.</p>
                    </div>
                    <div class="benefit-item">
                        <h3>🎮 Instant Play Technology</h3>
                        <p>No downloads, no installations, no waiting! This browser-based <em>Chiikawa Puzzle</em> loads instantly on any device with internet access, making brain training accessible anywhere, anytime.</p>
                    </div>
                    <div class="benefit-item">
                        <h3>👨‍👩‍👧‍👦 Family-Friendly Fun</h3>
                        <p>Perfect for all ages, <strong>Chiikawa Puzzle</strong> provides wholesome entertainment that parents can enjoy with children while promoting healthy cognitive development and family bonding.</p>
                    </div>
                    <div class="benefit-item">
                        <h3>🌟 Stress Relief & Relaxation</h3>
                        <p>The adorable Chiikawa character and gentle gameplay mechanics provide natural stress relief, making this puzzle game an ideal break from daily pressures and digital overwhelm.</p>
                    </div>
                    <div class="benefit-item">
                        <h3>📱 Cross-Platform Compatibility</h3>
                        <p>Whether you're on desktop, tablet, or smartphone, <em>Chiikawa Puzzle</em> adapts perfectly to your screen size and input method for optimal gaming experience across all devices.</p>
                    </div>
                    <div class="benefit-item">
                        <h3>🎯 Progressive Difficulty</h3>
                        <p>Each playthrough of <strong>Chiikawa Puzzle</strong> offers a unique challenge as your memory skills improve, ensuring long-term engagement and continuous cognitive development.</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- SEO Content Section -->
        <section id="seo-content">
            <div class="container">
                <h2>The Science Behind Chiikawa Puzzle Memory Training</h2>
                <div class="seo-content">
                    <p>Research in cognitive psychology demonstrates that <strong>memory puzzle games</strong> like our <em>Chiikawa Puzzle</em> significantly enhance neuroplasticity and cognitive function. The unique combination of visual memory training, spatial reasoning challenges, and time-pressure elements creates an optimal environment for brain development and maintenance.</p>
                    
                    <p>The <strong>Chiikawa character</strong>, beloved across Japan and internationally, provides the perfect subject matter for memory training due to its distinctive yet approachable design elements. The character's simple but memorable features - including the iconic round ears, expressive eyes, and compact body - create an ideal balance of complexity for effective memory exercise without overwhelming cognitive load.</p>
                    
                    <p>Our <em>Chiikawa Puzzle</em> implementation leverages cutting-edge web technologies to deliver smooth, responsive gameplay that maintains consistent performance across all modern browsers and devices. The game's architecture ensures minimal loading times and maximum accessibility, making cognitive training available to users regardless of their technical setup or internet speed.</p>
                    
                    <p>Players consistently report improved concentration, enhanced visual memory, and increased spatial awareness after regular <strong>Chiikawa Puzzle</strong> sessions. The game's design encourages daily practice through its quick session format - each complete game takes only 2-3 minutes, making it perfect for micro-learning sessions that fit into busy schedules.</p>
                    
                    <p>The social sharing aspect of <em>Chiikawa Puzzle</em> adds a community element that enhances motivation and engagement. Players can compare their memory-based recreations with friends and family, creating a supportive environment for cognitive improvement and friendly competition.</p>
                </div>
            </div>
        </section>

        <!-- FAQ Section -->
        <section id="faq">
            <div class="container">
                <h2>Frequently Asked Questions - Chiikawa Puzzle</h2>
                <div class="faq-content">
                    <div class="faq-item">
                        <h3>What is Chiikawa Puzzle and how does it work?</h3>
                        <p><strong>Chiikawa Puzzle</strong> is an innovative memory-based puzzle game featuring the beloved Japanese character Chiikawa. Players view the complete character for 5 seconds, then reconstruct it from scattered puzzle pieces using only their memory. This unique gameplay combines entertainment with cognitive training, making it perfect for improving memory skills while enjoying adorable characters.</p>
                    </div>
                    
                    <div class="faq-item">
                        <h3>Is Chiikawa Puzzle free to play?</h3>
                        <p>Yes! <em>Chiikawa Puzzle</em> is completely free to play online. No downloads, registrations, or payments required. Simply visit our website and start playing immediately on any device with an internet connection.</p>
                    </div>
                    
                    <div class="faq-item">
                        <h3>What devices can I play Chiikawa Puzzle on?</h3>
                        <p>Our <strong>Chiikawa Puzzle</strong> game is fully responsive and works perfectly on desktop computers, laptops, tablets, and smartphones. The game automatically adapts to your screen size for optimal gameplay experience across all modern browsers including Chrome, Firefox, Safari, and Edge.</p>
                    </div>
                    
                    <div class="faq-item">
                        <h3>How does Chiikawa Puzzle improve memory skills?</h3>
                        <p><em>Chiikawa Puzzle</em> enhances working memory, visual-spatial intelligence, and attention span through scientifically-designed gameplay. The 5-second memorization phase followed by reconstruction challenges exercises both short-term memory and spatial reasoning, leading to improved cognitive function with regular play.</p>
                    </div>
                    
                    <div class="faq-item">
                        <h3>Can children play Chiikawa Puzzle safely?</h3>
                        <p>Absolutely! <strong>Chiikawa Puzzle</strong> is designed to be family-friendly and suitable for all ages. The adorable Chiikawa character and gentle gameplay provide wholesome entertainment while promoting healthy cognitive development. Parents can enjoy playing alongside their children for quality family bonding time.</p>
                    </div>
                    
                    <div class="faq-item">
                        <h3>How long does each Chiikawa Puzzle game session take?</h3>
                        <p>Each complete <em>Chiikawa Puzzle</em> session takes only 2-3 minutes, making it perfect for quick brain training breaks. The short session format allows for easy integration into busy schedules while still providing effective cognitive exercise.</p>
                    </div>
                    
                    <div class="faq-item">
                        <h3>Can I share my Chiikawa Puzzle creations?</h3>
                        <p>Yes! <strong>Chiikawa Puzzle</strong> includes an integrated sharing feature that allows you to easily share your unique puzzle creations with friends and family. Each attempt creates a one-of-a-kind artwork that reflects your memory skills and creativity.</p>
                    </div>
                    
                    <div class="faq-item">
                        <h3>Do I need to download anything to play Chiikawa Puzzle?</h3>
                        <p>No downloads required! <em>Chiikawa Puzzle</em> is a browser-based game that runs entirely online. This instant-play technology ensures you can start training your brain immediately without any installation process or storage space concerns.</p>
                    </div>
                    
                    <div class="faq-item">
                        <h3>Is Chiikawa Puzzle officially licensed?</h3>
                        <p>This <strong>Chiikawa Puzzle</strong> game is a fan-made creation and is not officially affiliated with the Chiikawa franchise. It was created with love and respect for the character to provide fans with an engaging puzzle experience while promoting cognitive health and entertainment.</p>
                    </div>
                    
                    <div class="faq-item">
                        <h3>How often should I play Chiikawa Puzzle for best results?</h3>
                        <p>For optimal cognitive benefits, we recommend playing <em>Chiikawa Puzzle</em> daily for 10-15 minutes. The game's quick session format makes it easy to incorporate multiple rounds into your routine, and regular practice leads to noticeable improvements in memory and spatial reasoning skills.</p>
                    </div>
                </div>
            </div>
        </section>

    </main>

    <!-- Footer Section -->
    <footer>
        <div class="container">
            <p>&copy; 2025 Chiikawa Puzzle - The Ultimate Memory Training Experience. This is a fan-made game and is not affiliated with the official Chiikawa franchise.</p>
            <p>Created with ❤️ for Chiikawa fans everywhere!</p>
            <p>Keywords: Chiikawa Puzzle, Memory Game, Brain Training, Cognitive Enhancement, Japanese Character Game, Online Puzzle, Memory Challenge</p>
        </div>
    </footer>
    
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pixi.js/7.4.2/pixi.min.js"></script>
    <script type="module" src="translations/index.js"></script>
    <script src="modules/utils.js"></script>
    <script src="modules/assetManager.js"></script>
    <script src="modules/gameState.js"></script>
    <script type="module" src="modules/languageManager.js"></script>
    <script src="modules/ui.js"></script>
    <script src="game-refactored.js"></script>
    
    <script type="module">
        import { translations } from './translations/index.js';
        
        // Initialize language manager for website
        class WebsiteLanguageManager {
            constructor() {
                this.currentLanguage = 'zh';
                this.translations = translations;
                this.loadLanguagePreference();
            }
            
            loadLanguagePreference() {
                const saved = localStorage.getItem('chiikawa-language');
                if (saved && this.translations[saved]) {
                    this.currentLanguage = saved;
                }
            }
            
            setLanguage(lang) {
                if (this.translations[lang]) {
                    this.currentLanguage = lang;
                    localStorage.setItem('chiikawa-language', lang);
                }
            }
            
            getCurrentLanguage() {
                return this.currentLanguage;
            }
            
            getText(key) {
                const keys = key.split('.');
                let value = this.translations[this.currentLanguage];
                for (const k of keys) {
                    if (value && value[k]) {
                        value = value[k];
                    } else {
                        return key; // Return key if translation not found
                    }
                }
                return value;
            }
        }
        
        const websiteLanguageManager = new WebsiteLanguageManager();
        
        function updateWebsiteLanguage() {
            const currentLang = websiteLanguageManager.getCurrentLanguage();
            
            // Update page title
            document.title = websiteLanguageManager.getText('website.title');
            
            // Update meta description
            const metaDescription = document.querySelector('meta[name="description"]');
            if (metaDescription) {
                metaDescription.content = websiteLanguageManager.getText('website.description');
            }
            
            // Update header
            const logoH1 = document.querySelector('.logo-container h1');
            if (logoH1) logoH1.textContent = websiteLanguageManager.getText('website.title');
            
            // Update navigation
            const navLinks = document.querySelectorAll('.nav-menu a');
            const navKeys = ['home', 'howToPlay', 'features', 'faq'];
            navLinks.forEach((link, index) => {
                if (navKeys[index]) {
                    link.textContent = websiteLanguageManager.getText(`website.nav.${navKeys[index]}`);
                }
            });
            
            // Update hero section
            const heroTitle = document.querySelector('.hero h2');
            if (heroTitle) heroTitle.textContent = websiteLanguageManager.getText('website.hero.title');
            
            const heroSubtitle = document.querySelector('.hero p');
            if (heroSubtitle) heroSubtitle.textContent = websiteLanguageManager.getText('website.hero.subtitle');
            
            const playButton = document.querySelector('.cta-button');
            if (playButton) playButton.textContent = websiteLanguageManager.getText('website.hero.playButton');
            
            // Update features section
            const featuresTitle = document.querySelector('#features h2');
            if (featuresTitle) featuresTitle.textContent = websiteLanguageManager.getText('website.features.title');
            
            const featureCards = document.querySelectorAll('.feature');
            const featureKeys = ['adorable', 'memory', 'shareable', 'responsive'];
            featureCards.forEach((card, index) => {
                if (featureKeys[index]) {
                    const title = card.querySelector('h3');
                    const desc = card.querySelector('p');
                    if (title) title.textContent = websiteLanguageManager.getText(`website.features.${featureKeys[index]}.title`);
                    if (desc) desc.textContent = websiteLanguageManager.getText(`website.features.${featureKeys[index]}.description`);
                }
            });
            
            // Update how to play section
            const howToPlayTitle = document.querySelector('#how-to-play h2');
            if (howToPlayTitle) howToPlayTitle.textContent = websiteLanguageManager.getText('website.howToPlay.title');
            
            const steps = document.querySelectorAll('.step');
            steps.forEach((step, index) => {
                const title = step.querySelector('h3');
                const desc = step.querySelector('p');
                if (title) title.textContent = websiteLanguageManager.getText(`website.howToPlay.steps.${index}.title`);
                if (desc) desc.textContent = websiteLanguageManager.getText(`website.howToPlay.steps.${index}.description`);
            });
            
            // Update FAQ section
            const faqTitle = document.querySelector('#faq h2');
            if (faqTitle) faqTitle.textContent = websiteLanguageManager.getText('website.faq.title');
            
            const faqItems = document.querySelectorAll('.faq-item');
            faqItems.forEach((item, index) => {
                const question = item.querySelector('h3');
                const answer = item.querySelector('p');
                if (question) question.textContent = websiteLanguageManager.getText(`website.faq.items.${index}.question`);
                if (answer) answer.textContent = websiteLanguageManager.getText(`website.faq.items.${index}.answer`);
            });
            
            // Update footer
            const footerText = document.querySelector('footer p');
            if (footerText) footerText.textContent = websiteLanguageManager.getText('website.footer.copyright');
        }
        
        // Enhanced Language selector functionality
        document.addEventListener('DOMContentLoaded', function() {
            const languageSelector = document.getElementById('languageSelector');
            const languageButton = document.getElementById('languageButton');
            const languageDropdown = document.getElementById('languageDropdown');
            const languageSelect = document.getElementById('languageSelect');
            const languageOptions = document.querySelectorAll('.language-option');

            // Language data
            const languages = {
                zh: { flag: '🇨🇳', text: '中文' },
                ja: { flag: '🇯🇵', text: '日本語' },
                en: { flag: '🇺🇸', text: 'English' },
                ko: { flag: '🇰🇷', text: '한국어' },
                fr: { flag: '🇫🇷', text: 'Français' },
                de: { flag: '🇩🇪', text: 'Deutsch' },
                es: { flag: '🇪🇸', text: 'Español' },
                it: { flag: '🇮🇹', text: 'Italiano' },
                ru: { flag: '🇷🇺', text: 'Русский' },
                pt: { flag: '🇵🇹', text: 'Português' },
                th: { flag: '🇹🇭', text: 'ภาษาไทย' }
            };

            let isDropdownOpen = false;

            // Initialize website language
            updateWebsiteLanguage();

            // Set initial language in UI
            const currentLang = websiteLanguageManager.getCurrentLanguage();
            updateLanguageButton(currentLang);
            updateLanguageOptions(currentLang);
            languageSelect.value = currentLang;



            // Toggle dropdown
            function toggleDropdown() {
                isDropdownOpen = !isDropdownOpen;
                languageDropdown.classList.toggle('show', isDropdownOpen);
                languageButton.setAttribute('aria-expanded', isDropdownOpen);

                if (isDropdownOpen) {
                    // Focus first option when opening
                    const firstOption = languageDropdown.querySelector('.language-option');
                    if (firstOption) firstOption.focus();
                }
            }

            // Close dropdown
            function closeDropdown() {
                isDropdownOpen = false;
                languageDropdown.classList.remove('show');
                languageButton.setAttribute('aria-expanded', 'false');
                languageButton.focus();
            }

            // Update button display
            function updateLanguageButton(langCode) {
                const lang = languages[langCode];
                if (lang) {
                    languageButton.querySelector('.language-flag').textContent = lang.flag;
                    languageButton.querySelector('.language-text').textContent = lang.text;
                }
            }

            // Update option selection
            function updateLanguageOptions(selectedLang) {
                languageOptions.forEach(option => {
                    const isSelected = option.dataset.value === selectedLang;
                    option.setAttribute('aria-selected', isSelected);
                });
            }

            // Change language
            function changeLanguage(newLang) {
                websiteLanguageManager.setLanguage(newLang);
                updateWebsiteLanguage();
                updateLanguageButton(newLang);
                updateLanguageOptions(newLang);
                languageSelect.value = newLang;

                // Also update the game if it's running
                if (window.game && window.game.languageManager) {
                    window.game.languageManager.setLanguage(newLang);
                    window.game.updateUILanguage();
                    window.game.updateHTMLLanguage();
                }

                closeDropdown();
            }

            // Button click handler
            languageButton.addEventListener('click', toggleDropdown);

            // Option click handlers
            languageOptions.forEach(option => {
                option.addEventListener('click', function() {
                    changeLanguage(this.dataset.value);
                });
            });

            // Keyboard navigation
            languageButton.addEventListener('keydown', function(e) {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    toggleDropdown();
                } else if (e.key === 'ArrowDown') {
                    e.preventDefault();
                    if (!isDropdownOpen) {
                        toggleDropdown();
                    }
                }
            });

            languageDropdown.addEventListener('keydown', function(e) {
                const focusedOption = document.activeElement;
                const options = Array.from(languageOptions);
                const currentIndex = options.indexOf(focusedOption);

                switch (e.key) {
                    case 'ArrowDown':
                        e.preventDefault();
                        const nextIndex = (currentIndex + 1) % options.length;
                        options[nextIndex].focus();
                        break;
                    case 'ArrowUp':
                        e.preventDefault();
                        const prevIndex = currentIndex > 0 ? currentIndex - 1 : options.length - 1;
                        options[prevIndex].focus();
                        break;
                    case 'Enter':
                    case ' ':
                        e.preventDefault();
                        if (focusedOption && focusedOption.classList.contains('language-option')) {
                            changeLanguage(focusedOption.dataset.value);
                        }
                        break;
                    case 'Escape':
                        e.preventDefault();
                        closeDropdown();
                        break;
                }
            });

            // Close dropdown when clicking outside
            document.addEventListener('click', function(e) {
                if (!languageSelector.contains(e.target)) {
                    closeDropdown();
                }
            });

            // Fallback for original select element
            languageSelect.addEventListener('change', function() {
                changeLanguage(this.value);
            });
        });
        
        // Make websiteLanguageManager available globally
        window.websiteLanguageManager = websiteLanguageManager;
    </script>
    
    <!-- Mobile Menu Toggle Script -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const mobileMenuToggle = document.querySelector('.mobile-menu-toggle');
            const navMenu = document.querySelector('.nav-menu');
            const navLinks = document.querySelectorAll('.nav-menu a');
            
            // Toggle mobile menu
            mobileMenuToggle.addEventListener('click', function() {
                this.classList.toggle('active');
                navMenu.classList.toggle('active');
            });
            
            // Close menu when clicking on a link (but not on language selector)
            navLinks.forEach(link => {
                link.addEventListener('click', function() {
                    mobileMenuToggle.classList.remove('active');
                    navMenu.classList.remove('active');
                });
            });

            // Don't close menu when interacting with language selector
            const languageSelector = document.getElementById('languageSelector');
            if (languageSelector) {
                languageSelector.addEventListener('click', function(e) {
                    // Only stop propagation for dropdown clicks, not the button itself
                    if (e.target.closest('.language-dropdown')) {
                        e.stopPropagation();
                    }
                });
            }
            
            // Close menu when clicking outside
            document.addEventListener('click', function(event) {
                if (!event.target.closest('header')) {
                    mobileMenuToggle.classList.remove('active');
                    navMenu.classList.remove('active');
                }
            });
        });
    </script>
</body>
</html>