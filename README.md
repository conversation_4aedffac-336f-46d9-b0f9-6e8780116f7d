# Chiikawa Puzzle Game

A simple and fun puzzle game where you reassemble a picture of the character <PERSON><PERSON><PERSON>.

## Description

This is a web-based puzzle game built with HTML, CSS, and JavaScript, using the PixiJS library for rendering. The game has two main phases:

1.  **Memory Phase:** You are shown a complete picture of <PERSON><PERSON><PERSON> for a few seconds to memorize it.
2.  **Puzzling Phase:** The picture is broken into parts, and you have to place each part in its correct position on the canvas.

After placing all the parts, your creation is revealed, and you have the option to play again or download your masterpiece as a PNG image.

## Features

*   **Memory and Puzzle Gameplay:** A two-phase game that tests your memory and puzzle-solving skills.
*   **Interactive UI:** Buttons for starting the game, replaying, and downloading your creation.
*   **Downloadable Creations:** Save your finished puzzle as a PNG image.
*   **Modular Codebase:** The code is organized into modules for easy understanding and maintenance.

## Getting Started

To play the game, simply open the `index.html` file in your web browser.

## Project Structure

```
.
├── assets
│   └── images
│       ├── chiikawa_body.png
│       ├── chiikawa_foot.png
│       ├── chiikawa_full.png
│       └── chiikawa_head.png
├── modules
│   ├── assetManager.js
│   ├── gameState.js
│   ├── ui.js
│   └── utils.js
├── game-refactored.js
├── game.js
├── index.html
├── plan.md
└── style.css
```

*   **`index.html`**: The main entry point of the game.
*   **`style.css`**: Contains the styles for the game's UI.
*   **`game-refactored.js`**: The main game logic, built as a class-based modular system.
*   **`modules/`**: Contains the different modules of the game:
    *   **`assetManager.js`**: Manages the loading of game assets (images, etc.).
    *   **`gameState.js`**: Manages the state of the game (e.g., start screen, memory phase, etc.).
    *   **`ui.js`**: Creates and manages UI elements like buttons and text.
    *   **`utils.js`**: Provides utility functions for the game.
*   **`assets/`**: Contains all the game's assets.

## Technologies Used

*   **HTML5**
*   **CSS3**
*   **JavaScript (ES6)**
*   **PixiJS:** A fast and lightweight 2D rendering library.
