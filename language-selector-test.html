<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>语言选择器测试 - Language Selector Test</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 40px;
            background: linear-gradient(135deg, #f0f8f0, #e8f5e8);
            min-height: 100vh;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 40px;
            border-radius: 20px;
            box-shadow: 0 8px 32px rgba(74, 138, 74, 0.15);
        }
        
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 40px;
            padding: 20px;
            background: linear-gradient(135deg, #4a8a4a, #5a9a5a);
            color: white;
            border-radius: 15px;
        }
        
        .test-content {
            padding: 20px 0;
        }
        
        .language-demo {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            border-left: 4px solid #4a8a4a;
        }
        
        .current-language {
            font-weight: bold;
            color: #4a8a4a;
            margin-bottom: 10px;
        }
        
        /* Include the language selector styles */
        .language-selector {
            position: relative;
            display: inline-block;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        
        .language-select-hidden {
            position: absolute;
            opacity: 0;
            pointer-events: none;
            z-index: -1;
        }
        
        .language-select-button {
            background: linear-gradient(135deg, #4a8a4a, #5a9a5a);
            color: #ffffff;
            border: 2px solid rgba(255, 255, 255, 0.3);
            padding: 10px 16px;
            border-radius: 25px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 4px 12px rgba(74, 138, 74, 0.25);
            outline: none;
            min-width: 120px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            gap: 8px;
            backdrop-filter: blur(10px);
            letter-spacing: 0.5px;
            position: relative;
            overflow: hidden;
        }
        
        .language-flag {
            font-size: 16px;
            line-height: 1;
            flex-shrink: 0;
        }
        
        .language-text {
            flex: 1;
            text-align: center;
            white-space: nowrap;
        }
        
        .language-arrow {
            font-size: 10px;
            transition: transform 0.3s ease;
            flex-shrink: 0;
        }
        
        .language-select-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(74, 138, 74, 0.35);
            background: linear-gradient(135deg, #5a9a5a, #6aaa6a);
            border-color: rgba(255, 255, 255, 0.5);
        }
        
        .language-select-button:hover .language-arrow {
            transform: rotate(180deg);
        }
        
        .language-select-button[aria-expanded="true"] .language-arrow {
            transform: rotate(180deg);
        }
        
        .language-dropdown {
            position: absolute;
            top: calc(100% + 8px);
            left: 0;
            right: 0;
            background: rgba(255, 255, 255, 0.95);
            border: 2px solid rgba(74, 138, 74, 0.2);
            border-radius: 16px;
            box-shadow: 0 8px 32px rgba(74, 138, 74, 0.2);
            backdrop-filter: blur(20px);
            z-index: 1000;
            opacity: 0;
            visibility: hidden;
            transform: translateY(-10px) scale(0.95);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            list-style: none;
            margin: 0;
            padding: 8px;
            overflow: hidden;
            max-height: 0;
        }
        
        .language-dropdown.show {
            opacity: 1;
            visibility: visible;
            transform: translateY(0) scale(1);
            max-height: 200px;
        }
        
        .language-option {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px 16px;
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            color: #2d5a2d;
            font-weight: 500;
            font-size: 14px;
            position: relative;
            overflow: hidden;
        }
        
        .language-option:hover {
            background: linear-gradient(135deg, #f0f8f0, #e8f5e8);
            transform: translateX(4px);
            box-shadow: 0 2px 8px rgba(74, 138, 74, 0.15);
        }
        
        .language-option[aria-selected="true"] {
            background: linear-gradient(135deg, #4a8a4a, #5a9a5a);
            color: #ffffff;
            font-weight: 600;
            box-shadow: 0 2px 8px rgba(74, 138, 74, 0.3);
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="header">
            <h1 id="pageTitle">语言选择器测试</h1>
            <div class="language-selector" id="languageSelector">
                <button class="language-select-button" id="languageButton" aria-label="选择语言 / Select Language / 言語を選択" aria-expanded="false" aria-haspopup="listbox">
                    <span class="language-flag">🇨🇳</span>
                    <span class="language-text">中文</span>
                    <span class="language-arrow">▼</span>
                </button>
                <ul class="language-dropdown" id="languageDropdown" role="listbox" aria-label="Language options">
                    <li class="language-option" data-value="zh" role="option" aria-selected="true">
                        <span class="language-flag">🇨🇳</span>
                        <span class="language-text">中文</span>
                    </li>
                    <li class="language-option" data-value="ja" role="option" aria-selected="false">
                        <span class="language-flag">🇯🇵</span>
                        <span class="language-text">日本語</span>
                    </li>
                    <li class="language-option" data-value="en" role="option" aria-selected="false">
                        <span class="language-flag">🇺🇸</span>
                        <span class="language-text">English</span>
                    </li>
                </ul>
            </div>
        </div>
        
        <div class="test-content">
            <div class="current-language" id="currentLang">当前语言: 中文</div>
            
            <div class="language-demo">
                <h3 id="demoTitle">演示内容</h3>
                <p id="demoText">这是一个语言选择器的演示。点击右上角的语言按钮来切换不同的语言。</p>
                <ul>
                    <li id="feature1">美观的设计</li>
                    <li id="feature2">流畅的动画</li>
                    <li id="feature3">键盘导航支持</li>
                    <li id="feature4">响应式布局</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        // Simple translations for testing
        const testTranslations = {
            zh: {
                pageTitle: '语言选择器测试',
                currentLang: '当前语言: 中文',
                demoTitle: '演示内容',
                demoText: '这是一个语言选择器的演示。点击右上角的语言按钮来切换不同的语言。',
                feature1: '美观的设计',
                feature2: '流畅的动画',
                feature3: '键盘导航支持',
                feature4: '响应式布局'
            },
            ja: {
                pageTitle: '言語セレクターテスト',
                currentLang: '現在の言語: 日本語',
                demoTitle: 'デモコンテンツ',
                demoText: 'これは言語セレクターのデモです。右上の言語ボタンをクリックして異なる言語に切り替えてください。',
                feature1: '美しいデザイン',
                feature2: 'スムーズなアニメーション',
                feature3: 'キーボードナビゲーション対応',
                feature4: 'レスポンシブレイアウト'
            },
            en: {
                pageTitle: 'Language Selector Test',
                currentLang: 'Current Language: English',
                demoTitle: 'Demo Content',
                demoText: 'This is a demonstration of the language selector. Click the language button in the top right to switch between different languages.',
                feature1: 'Beautiful Design',
                feature2: 'Smooth Animations',
                feature3: 'Keyboard Navigation Support',
                feature4: 'Responsive Layout'
            }
        };

        // Language selector implementation (same as main site)
        document.addEventListener('DOMContentLoaded', function() {
            const languageSelector = document.getElementById('languageSelector');
            const languageButton = document.getElementById('languageButton');
            const languageDropdown = document.getElementById('languageDropdown');
            const languageOptions = document.querySelectorAll('.language-option');
            
            const languages = {
                zh: { flag: '🇨🇳', text: '中文' },
                ja: { flag: '🇯🇵', text: '日本語' },
                en: { flag: '🇺🇸', text: 'English' }
            };
            
            let isDropdownOpen = false;
            let currentLanguage = 'zh';
            
            function updateContent(lang) {
                const translations = testTranslations[lang];
                document.getElementById('pageTitle').textContent = translations.pageTitle;
                document.getElementById('currentLang').textContent = translations.currentLang;
                document.getElementById('demoTitle').textContent = translations.demoTitle;
                document.getElementById('demoText').textContent = translations.demoText;
                document.getElementById('feature1').textContent = translations.feature1;
                document.getElementById('feature2').textContent = translations.feature2;
                document.getElementById('feature3').textContent = translations.feature3;
                document.getElementById('feature4').textContent = translations.feature4;
            }
            
            function toggleDropdown() {
                isDropdownOpen = !isDropdownOpen;
                languageDropdown.classList.toggle('show', isDropdownOpen);
                languageButton.setAttribute('aria-expanded', isDropdownOpen);
            }
            
            function closeDropdown() {
                isDropdownOpen = false;
                languageDropdown.classList.remove('show');
                languageButton.setAttribute('aria-expanded', 'false');
            }
            
            function updateLanguageButton(langCode) {
                const lang = languages[langCode];
                if (lang) {
                    languageButton.querySelector('.language-flag').textContent = lang.flag;
                    languageButton.querySelector('.language-text').textContent = lang.text;
                }
            }
            
            function updateLanguageOptions(selectedLang) {
                languageOptions.forEach(option => {
                    const isSelected = option.dataset.value === selectedLang;
                    option.setAttribute('aria-selected', isSelected);
                });
            }
            
            function changeLanguage(newLang) {
                currentLanguage = newLang;
                updateLanguageButton(newLang);
                updateLanguageOptions(newLang);
                updateContent(newLang);
                closeDropdown();
            }
            
            languageButton.addEventListener('click', toggleDropdown);
            
            languageOptions.forEach(option => {
                option.addEventListener('click', function() {
                    changeLanguage(this.dataset.value);
                });
            });
            
            document.addEventListener('click', function(e) {
                if (!languageSelector.contains(e.target)) {
                    closeDropdown();
                }
            });
        });
    </script>
</body>
</html>
