// Utils Module - Helper functions and utilities
class GameUtils {
    static createImageSprite(textureUrl) {
        try {
            const texture = PIXI.Texture.from(textureUrl);
            return new PIXI.Sprite(texture);
        } catch (error) {
            console.warn(`Failed to load texture: ${textureUrl}`);
            const placeholder = new PIXI.Graphics();
            placeholder.beginFill(0xCCCCCC);
            placeholder.drawRect(0, 0, 100, 100);
            placeholder.endFill();
            
            const text = new PIXI.Text('Missing\nImage', {
                fontFamily: 'Arial',
                fontSize: 12,
                fill: 0x666666,
                align: 'center'
            });
            text.anchor.set(0.5);
            text.x = 50;
            text.y = 50;
            placeholder.addChild(text);
            
            return placeholder;
        }
    }

    static scaleToFit(sprite, maxWidth, maxHeight) {
        const scaleX = maxWidth / sprite.width;
        const scaleY = maxHeight / sprite.height;
        const scale = Math.min(scaleX, scaleY);
        sprite.scale.set(scale);
    }

    static clampToScreen(x, y, screenWidth, screenHeight) {
        return {
            x: Math.max(0, Math.min(screenWidth, x)),
            y: Math.max(0, Math.min(screenHeight, y))
        };
    }

    static getMousePosition(event, canvas, screenWidth, screenHeight) {
        const rect = canvas.getBoundingClientRect();
        const x = (event.clientX - rect.left) * (screenWidth / rect.width);
        const y = (event.clientY - rect.top) * (screenHeight / rect.height);
        
        return this.clampToScreen(x, y, screenWidth, screenHeight);
    }

    static downloadCanvas(canvas, filename = 'chiikawa-creation.png') {
        const link = document.createElement('a');
        link.download = filename;
        link.href = canvas.toDataURL('image/png');
        link.click();
    }

    static createCanvasWithBackground(width, height, backgroundColor = '#FFFFFF') {
        const canvas = document.createElement('canvas');
        canvas.width = width;
        canvas.height = height;
        const ctx = canvas.getContext('2d');
        
        ctx.fillStyle = backgroundColor;
        ctx.fillRect(0, 0, width, height);
        
        return { canvas, ctx };
    }

    static copySprite(original) {
        if (!original.texture) return null;
        
        const sprite = new PIXI.Sprite(original.texture);
        sprite.x = original.x;
        sprite.y = original.y;
        sprite.anchor.copyFrom(original.anchor);
        sprite.scale.copyFrom(original.scale);
        sprite.rotation = original.rotation;
        sprite.alpha = original.alpha;
        
        return sprite;
    }

    static animateProperty(object, property, targetValue, duration = 1000, onComplete = null) {
        const startValue = object[property];
        const difference = targetValue - startValue;
        const startTime = Date.now();

        const animate = () => {
            const elapsed = Date.now() - startTime;
            const progress = Math.min(elapsed / duration, 1);
            
            // Ease out function
            const easedProgress = 1 - Math.pow(1 - progress, 3);
            
            object[property] = startValue + (difference * easedProgress);
            
            if (progress < 1) {
                requestAnimationFrame(animate);
            } else if (onComplete) {
                onComplete();
            }
        };
        
        animate();
    }

    static lerp(start, end, factor) {
        return start + (end - start) * factor;
    }

    static distance(x1, y1, x2, y2) {
        const dx = x2 - x1;
        const dy = y2 - y1;
        return Math.sqrt(dx * dx + dy * dy);
    }

    static randomBetween(min, max) {
        return Math.random() * (max - min) + min;
    }

    static randomChoice(array) {
        return array[Math.floor(Math.random() * array.length)];
    }
}

window.GameUtils = GameUtils;