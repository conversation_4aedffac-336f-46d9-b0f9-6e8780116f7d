// Language Manager Module - Handles internationalization and localization
class LanguageManager {
    constructor() {
        this.currentLanguage = 'zh'; // Default to Chinese
        this.translations = {};
        this.supportedLanguages = ['zh', 'ja']; // Chinese and Japanese
        this.callbacks = [];
        
        // Load translations and saved language preference
        this.loadTranslations();
        this.loadLanguagePreference();
    }

    async loadTranslations() {
        try {
            // Import translations dynamically
            const translationsModule = await import('../translations/index.js');
            this.translations = translationsModule.translations;
        } catch (error) {
            console.error('Failed to load translations:', error);
            this.translations = {};
        }
    }

    // Load language preference from localStorage
    loadLanguagePreference() {
        const savedLang = localStorage.getItem('chiikawa_language');
        if (savedLang && this.supportedLanguages.includes(savedLang)) {
            this.currentLanguage = savedLang;
        }
    }

    // Save language preference to localStorage
    saveLanguagePreference() {
        localStorage.setItem('chiikawa_language', this.currentLanguage);
    }

    // Set translations data
    setTranslations(translations) {
        this.translations = translations;
    }

    // Get current language
    getCurrentLanguage() {
        return this.currentLanguage;
    }

    // Get supported languages
    getSupportedLanguages() {
        return this.supportedLanguages;
    }

    // Switch language
    setLanguage(languageCode) {
        if (!this.supportedLanguages.includes(languageCode)) {
            console.warn(`Language ${languageCode} is not supported`);
            return false;
        }

        const oldLanguage = this.currentLanguage;
        this.currentLanguage = languageCode;
        this.saveLanguagePreference();

        // Notify all registered callbacks
        this.callbacks.forEach(callback => {
            try {
                callback(languageCode, oldLanguage);
            } catch (error) {
                console.error('Error in language change callback:', error);
            }
        });

        return true;
    }

    // Register callback for language changes
    onLanguageChange(callback) {
        this.callbacks.push(callback);
    }

    // Remove callback
    removeLanguageChangeCallback(callback) {
        const index = this.callbacks.indexOf(callback);
        if (index > -1) {
            this.callbacks.splice(index, 1);
        }
    }

    // Get translated text
    getText(key, params = {}) {
        const translation = this.getTranslation(key);
        
        if (!translation) {
            console.warn(`Translation not found for key: ${key}`);
            return key; // Return key as fallback
        }

        // Replace parameters in translation
        return this.replaceParameters(translation, params);
    }

    // Get translation for current language
    getTranslation(key) {
        const langTranslations = this.translations[this.currentLanguage];
        if (!langTranslations) {
            console.warn(`No translations found for language: ${this.currentLanguage}`);
            return null;
        }

        // Support nested keys like 'game.title'
        const keys = key.split('.');
        let result = langTranslations;
        
        for (const k of keys) {
            if (result && typeof result === 'object' && k in result) {
                result = result[k];
            } else {
                return null;
            }
        }

        return result;
    }

    // Replace parameters in translation string
    replaceParameters(text, params) {
        if (!params || Object.keys(params).length === 0) {
            return text;
        }

        let result = text;
        for (const [key, value] of Object.entries(params)) {
            const placeholder = `{${key}}`;
            result = result.replace(new RegExp(placeholder, 'g'), value);
        }

        return result;
    }

    // Get language display name
    getLanguageDisplayName(languageCode) {
        const displayNames = {
            'zh': '中文',
            'ja': '日本語'
        };
        return displayNames[languageCode] || languageCode;
    }

    // Toggle between supported languages
    toggleLanguage() {
        const currentIndex = this.supportedLanguages.indexOf(this.currentLanguage);
        const nextIndex = (currentIndex + 1) % this.supportedLanguages.length;
        const nextLanguage = this.supportedLanguages[nextIndex];
        
        return this.setLanguage(nextLanguage);
    }

    // Check if a language is supported
    isLanguageSupported(languageCode) {
        return this.supportedLanguages.includes(languageCode);
    }

    // Get all translations for debugging
    getAllTranslations() {
        return this.translations;
    }

    // Clear all translations
    clearTranslations() {
        this.translations = {};
    }
}

window.LanguageManager = LanguageManager;