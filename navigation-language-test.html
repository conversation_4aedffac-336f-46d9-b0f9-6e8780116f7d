<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>导航栏语言选择器测试</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #f0f8f0, #e8f5e8);
            min-height: 100vh;
        }
        
        /* Header styles */
        header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(15px);
            box-shadow: 0 4px 20px rgba(45, 90, 45, 0.1);
            position: sticky;
            top: 0;
            z-index: 100;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 40px;
        }
        
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px 0;
        }
        
        .logo-container {
            display: flex;
            align-items: center;
            gap: 16px;
        }
        
        .logo {
            width: 48px;
            height: 48px;
            background: linear-gradient(135deg, #4a8a4a, #5a9a5a);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 20px;
        }
        
        h1 {
            font-family: 'Fredoka One', sans-serif;
            color: #2d5a2d;
            font-size: 2.8rem;
            margin: 0;
            font-weight: 400;
        }
        
        /* Navigation styles */
        .nav-menu ul {
            display: flex;
            justify-content: center;
            align-items: center;
            list-style: none;
            gap: 40px;
            margin: 0;
            padding: 0;
        }
        
        .nav-menu ul li {
            position: relative;
        }
        
        .nav-menu ul li a {
            text-decoration: none;
            color: #4a7c4a;
            font-size: 1.1rem;
            font-weight: 600;
            padding: 12px 20px;
            border-radius: 25px;
            transition: all 0.3s ease;
            position: relative;
            background: linear-gradient(135deg, #c8e6c8, #b8ddb8);
            border: 2px solid transparent;
        }
        
        .nav-menu ul li a:hover {
            color: #ffffff;
            background: linear-gradient(135deg, #5a9a5a, #4a8a4a);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(45, 90, 45, 0.3);
        }
        
        /* Language selector in navigation */
        .nav-language-item {
            display: flex;
            align-items: center;
            position: relative;
        }
        
        .nav-language-item::before {
            content: '';
            position: absolute;
            left: -20px;
            top: 50%;
            transform: translateY(-50%);
            width: 1px;
            height: 24px;
            background: linear-gradient(to bottom, transparent, #a8d5a8, transparent);
            opacity: 0.6;
        }
        
        .language-selector {
            position: relative;
            display: inline-block;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        
        .language-select-hidden {
            position: absolute;
            opacity: 0;
            pointer-events: none;
            z-index: -1;
        }
        
        .language-select-button {
            background: linear-gradient(135deg, #c8e6c8, #b8ddb8);
            color: #4a7c4a;
            border: 2px solid transparent;
            padding: 12px 20px;
            border-radius: 25px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            outline: none;
            min-width: 120px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            gap: 8px;
            letter-spacing: 0.5px;
            position: relative;
            overflow: hidden;
        }
        
        .language-flag {
            font-size: 16px;
            line-height: 1;
            flex-shrink: 0;
        }
        
        .language-text {
            flex: 1;
            text-align: center;
            white-space: nowrap;
        }
        
        .language-arrow {
            font-size: 10px;
            transition: transform 0.3s ease;
            flex-shrink: 0;
        }
        
        .language-select-button:hover {
            color: #ffffff;
            background: linear-gradient(135deg, #5a9a5a, #4a8a4a);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(45, 90, 45, 0.3);
        }
        
        .language-select-button:hover .language-arrow {
            transform: rotate(180deg);
        }
        
        .language-select-button[aria-expanded="true"] .language-arrow {
            transform: rotate(180deg);
        }
        
        .language-dropdown {
            position: absolute;
            top: calc(100% + 8px);
            left: 0;
            right: 0;
            background: rgba(255, 255, 255, 0.95);
            border: 2px solid rgba(74, 138, 74, 0.2);
            border-radius: 16px;
            box-shadow: 0 8px 32px rgba(74, 138, 74, 0.2);
            backdrop-filter: blur(20px);
            z-index: 1000;
            opacity: 0;
            visibility: hidden;
            transform: translateY(-10px) scale(0.95);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            list-style: none;
            margin: 0;
            padding: 8px;
            overflow: hidden;
            max-height: 0;
        }
        
        .language-dropdown.show {
            opacity: 1;
            visibility: visible;
            transform: translateY(0) scale(1);
            max-height: 200px;
        }
        
        .language-option {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px 16px;
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            color: #2d5a2d;
            font-weight: 500;
            font-size: 14px;
            position: relative;
            overflow: hidden;
        }
        
        .language-option:hover {
            background: linear-gradient(135deg, #f0f8f0, #e8f5e8);
            transform: translateX(4px);
            box-shadow: 0 2px 8px rgba(74, 138, 74, 0.15);
        }
        
        .language-option[aria-selected="true"] {
            background: linear-gradient(135deg, #4a8a4a, #5a9a5a);
            color: #ffffff;
            font-weight: 600;
            box-shadow: 0 2px 8px rgba(74, 138, 74, 0.3);
        }
        
        /* Mobile menu toggle */
        .mobile-menu-toggle {
            display: none;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            width: 40px;
            height: 40px;
            background: transparent;
            border: none;
            cursor: pointer;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        
        .mobile-menu-toggle span {
            width: 25px;
            height: 3px;
            background: #2d5a2d;
            margin: 3px 0;
            transition: all 0.3s ease;
            border-radius: 2px;
        }
        
        /* Content area */
        .content {
            padding: 60px 0;
        }
        
        .demo-section {
            background: white;
            padding: 40px;
            border-radius: 20px;
            box-shadow: 0 8px 32px rgba(74, 138, 74, 0.15);
            margin-bottom: 40px;
        }
        
        .current-language {
            font-weight: bold;
            color: #4a8a4a;
            margin-bottom: 20px;
            font-size: 18px;
        }
        
        /* Mobile responsive */
        @media (max-width: 900px) {
            .mobile-menu-toggle {
                display: flex;
            }
            
            .nav-menu {
                display: none;
                position: absolute;
                top: 100%;
                left: 0;
                right: 0;
                background: rgba(255, 255, 255, 0.98);
                backdrop-filter: blur(15px);
                box-shadow: 0 8px 32px rgba(45, 90, 45, 0.15);
                border-top: 1px solid #a8d5a8;
                padding: 20px 0;
            }
            
            .nav-menu.active {
                display: block;
            }
            
            .nav-menu ul {
                flex-direction: column;
                align-items: center;
                gap: 16px;
            }
            
            .nav-menu ul li a {
                display: block;
                padding: 12px 24px;
                width: 200px;
                text-align: center;
                border-radius: 12px;
                font-size: 1rem;
            }
            
            .nav-language-item {
                justify-content: center;
                margin-top: 16px;
                padding: 0 24px;
            }
            
            .nav-language-item::before {
                display: none;
            }
            
            .language-select-button {
                width: 200px;
                justify-content: center;
                gap: 12px;
            }
        }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <div class="header-content">
                <div class="logo-container">
                    <div class="logo">C</div>
                    <h1 id="pageTitle">Chiikawa Puzzle</h1>
                </div>
                <button class="mobile-menu-toggle" aria-label="Toggle mobile menu">
                    <span></span>
                    <span></span>
                    <span></span>
                </button>
            </div>
            <nav class="nav-menu">
                <ul>
                    <li><a href="#game" id="navGame">Game</a></li>
                    <li><a href="#how-to-play" id="navHowTo">How to Play</a></li>
                    <li><a href="#features" id="navFeatures">Features</a></li>
                    <li><a href="#faq" id="navFaq">FAQ</a></li>
                    <li class="nav-language-item">
                        <div class="language-selector" id="languageSelector">
                            <button class="language-select-button" id="languageButton" aria-label="选择语言 / Select Language / 言語を選択" aria-expanded="false" aria-haspopup="listbox">
                                <span class="language-flag">🇨🇳</span>
                                <span class="language-text">中文</span>
                                <span class="language-arrow">▼</span>
                            </button>
                            <ul class="language-dropdown" id="languageDropdown" role="listbox" aria-label="Language options">
                                <li class="language-option" data-value="zh" role="option" aria-selected="true">
                                    <span class="language-flag">🇨🇳</span>
                                    <span class="language-text">中文</span>
                                </li>
                                <li class="language-option" data-value="ja" role="option" aria-selected="false">
                                    <span class="language-flag">🇯🇵</span>
                                    <span class="language-text">日本語</span>
                                </li>
                                <li class="language-option" data-value="en" role="option" aria-selected="false">
                                    <span class="language-flag">🇺🇸</span>
                                    <span class="language-text">English</span>
                                </li>
                            </ul>
                        </div>
                    </li>
                </ul>
            </nav>
        </div>
    </header>

    <main>
        <div class="container">
            <div class="content">
                <div class="demo-section">
                    <div class="current-language" id="currentLang">当前语言: 中文</div>
                    <h2 id="demoTitle">导航栏语言选择器演示</h2>
                    <p id="demoText">语言选择器现在位于导航栏的FAQ旁边，提供更好的可访问性和用户体验。</p>
                    
                    <h3 id="featuresTitle">特性:</h3>
                    <ul>
                        <li id="feature1">与导航栏完美融合的设计</li>
                        <li id="feature2">桌面和移动端的响应式布局</li>
                        <li id="feature3">优雅的分隔符设计</li>
                        <li id="feature4">保持一致的交互体验</li>
                    </ul>
                </div>
            </div>
        </div>
    </main>

    <script>
        // Test translations
        const translations = {
            zh: {
                pageTitle: 'Chiikawa Puzzle',
                navGame: 'Game',
                navHowTo: 'How to Play',
                navFeatures: 'Features',
                navFaq: 'FAQ',
                currentLang: '当前语言: 中文',
                demoTitle: '导航栏语言选择器演示',
                demoText: '语言选择器现在位于导航栏的FAQ旁边，提供更好的可访问性和用户体验。',
                featuresTitle: '特性:',
                feature1: '与导航栏完美融合的设计',
                feature2: '桌面和移动端的响应式布局',
                feature3: '优雅的分隔符设计',
                feature4: '保持一致的交互体验'
            },
            ja: {
                pageTitle: 'ちいかわパズル',
                navGame: 'ゲーム',
                navHowTo: '遊び方',
                navFeatures: '機能',
                navFaq: 'FAQ',
                currentLang: '現在の言語: 日本語',
                demoTitle: 'ナビゲーション言語セレクターデモ',
                demoText: '言語セレクターはナビゲーションバーのFAQの隣に配置され、より良いアクセシビリティとユーザーエクスペリエンスを提供します。',
                featuresTitle: '特徴:',
                feature1: 'ナビゲーションバーと完璧に統合されたデザイン',
                feature2: 'デスクトップとモバイルのレスポンシブレイアウト',
                feature3: 'エレガントなセパレーターデザイン',
                feature4: '一貫したインタラクション体験'
            },
            en: {
                pageTitle: 'Chiikawa Puzzle',
                navGame: 'Game',
                navHowTo: 'How to Play',
                navFeatures: 'Features',
                navFaq: 'FAQ',
                currentLang: 'Current Language: English',
                demoTitle: 'Navigation Language Selector Demo',
                demoText: 'The language selector is now positioned next to FAQ in the navigation bar, providing better accessibility and user experience.',
                featuresTitle: 'Features:',
                feature1: 'Seamlessly integrated design with navigation bar',
                feature2: 'Responsive layout for desktop and mobile',
                feature3: 'Elegant separator design',
                feature4: 'Consistent interaction experience'
            }
        };

        // Language selector functionality
        document.addEventListener('DOMContentLoaded', function() {
            const languageSelector = document.getElementById('languageSelector');
            const languageButton = document.getElementById('languageButton');
            const languageDropdown = document.getElementById('languageDropdown');
            const languageOptions = document.querySelectorAll('.language-option');
            const mobileMenuToggle = document.querySelector('.mobile-menu-toggle');
            const navMenu = document.querySelector('.nav-menu');
            
            const languages = {
                zh: { flag: '🇨🇳', text: '中文' },
                ja: { flag: '🇯🇵', text: '日本語' },
                en: { flag: '🇺🇸', text: 'English' }
            };
            
            let isDropdownOpen = false;
            let currentLanguage = 'zh';
            
            function updateContent(lang) {
                const t = translations[lang];
                document.getElementById('pageTitle').textContent = t.pageTitle;
                document.getElementById('navGame').textContent = t.navGame;
                document.getElementById('navHowTo').textContent = t.navHowTo;
                document.getElementById('navFeatures').textContent = t.navFeatures;
                document.getElementById('navFaq').textContent = t.navFaq;
                document.getElementById('currentLang').textContent = t.currentLang;
                document.getElementById('demoTitle').textContent = t.demoTitle;
                document.getElementById('demoText').textContent = t.demoText;
                document.getElementById('featuresTitle').textContent = t.featuresTitle;
                document.getElementById('feature1').textContent = t.feature1;
                document.getElementById('feature2').textContent = t.feature2;
                document.getElementById('feature3').textContent = t.feature3;
                document.getElementById('feature4').textContent = t.feature4;
            }
            
            function toggleDropdown() {
                isDropdownOpen = !isDropdownOpen;
                languageDropdown.classList.toggle('show', isDropdownOpen);
                languageButton.setAttribute('aria-expanded', isDropdownOpen);
            }
            
            function closeDropdown() {
                isDropdownOpen = false;
                languageDropdown.classList.remove('show');
                languageButton.setAttribute('aria-expanded', 'false');
            }
            
            function updateLanguageButton(langCode) {
                const lang = languages[langCode];
                if (lang) {
                    languageButton.querySelector('.language-flag').textContent = lang.flag;
                    languageButton.querySelector('.language-text').textContent = lang.text;
                }
            }
            
            function updateLanguageOptions(selectedLang) {
                languageOptions.forEach(option => {
                    const isSelected = option.dataset.value === selectedLang;
                    option.setAttribute('aria-selected', isSelected);
                });
            }
            
            function changeLanguage(newLang) {
                currentLanguage = newLang;
                updateLanguageButton(newLang);
                updateLanguageOptions(newLang);
                updateContent(newLang);
                closeDropdown();
            }
            
            // Language selector events
            languageButton.addEventListener('click', toggleDropdown);
            
            languageOptions.forEach(option => {
                option.addEventListener('click', function() {
                    changeLanguage(this.dataset.value);
                });
            });
            
            document.addEventListener('click', function(e) {
                if (!languageSelector.contains(e.target)) {
                    closeDropdown();
                }
            });
            
            // Mobile menu events
            mobileMenuToggle.addEventListener('click', function() {
                this.classList.toggle('active');
                navMenu.classList.toggle('active');
            });
            
            // Don't close menu when interacting with language selector
            languageSelector.addEventListener('click', function(e) {
                e.stopPropagation();
            });
        });
    </script>
</body>
</html>
