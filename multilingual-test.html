<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>多语言测试 - Multilingual Test</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #f0f8f0, #e8f5e8);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 8px 32px rgba(74, 138, 74, 0.15);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #4a8a4a, #5a9a5a);
            color: white;
            padding: 30px;
            text-align: center;
            position: relative;
        }
        
        .language-selector {
            position: absolute;
            top: 20px;
            right: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON>o, sans-serif;
        }
        
        .language-select-hidden {
            position: absolute;
            opacity: 0;
            pointer-events: none;
            z-index: -1;
        }
        
        .language-select-button {
            background: rgba(255, 255, 255, 0.2);
            color: #ffffff;
            border: 2px solid rgba(255, 255, 255, 0.3);
            padding: 10px 16px;
            border-radius: 25px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            outline: none;
            min-width: 140px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            gap: 8px;
            letter-spacing: 0.5px;
            position: relative;
            overflow: hidden;
        }
        
        .language-flag {
            font-size: 16px;
            line-height: 1;
            flex-shrink: 0;
        }
        
        .language-text {
            flex: 1;
            text-align: center;
            white-space: nowrap;
        }
        
        .language-arrow {
            font-size: 10px;
            transition: transform 0.3s ease;
            flex-shrink: 0;
        }
        
        .language-select-button:hover {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.5);
            transform: translateY(-2px);
        }
        
        .language-select-button[aria-expanded="true"] .language-arrow {
            transform: rotate(180deg);
        }
        
        .language-dropdown {
            position: absolute;
            top: calc(100% + 8px);
            left: 0;
            right: 0;
            background: rgba(255, 255, 255, 0.95);
            border: 2px solid rgba(74, 138, 74, 0.2);
            border-radius: 16px;
            box-shadow: 0 8px 32px rgba(74, 138, 74, 0.2);
            backdrop-filter: blur(20px);
            z-index: 1000;
            opacity: 0;
            visibility: hidden;
            transform: translateY(-10px) scale(0.95);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            list-style: none;
            margin: 0;
            padding: 8px;
            overflow-y: auto;
            overflow-x: hidden;
            max-height: 0;
        }
        
        .language-dropdown.show {
            opacity: 1;
            visibility: visible;
            transform: translateY(0) scale(1);
            max-height: 300px;
        }
        
        .language-option {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px 16px;
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            color: #2d5a2d;
            font-weight: 500;
            font-size: 14px;
            position: relative;
            overflow: hidden;
        }
        
        .language-option:hover {
            background: linear-gradient(135deg, #f0f8f0, #e8f5e8);
            transform: translateX(4px);
        }
        
        .language-option[aria-selected="true"] {
            background: linear-gradient(135deg, #4a8a4a, #5a9a5a);
            color: #ffffff;
            font-weight: 600;
        }
        
        .language-dropdown::-webkit-scrollbar {
            width: 6px;
        }
        
        .language-dropdown::-webkit-scrollbar-track {
            background: rgba(74, 138, 74, 0.1);
            border-radius: 3px;
        }
        
        .language-dropdown::-webkit-scrollbar-thumb {
            background: rgba(74, 138, 74, 0.3);
            border-radius: 3px;
        }
        
        .content {
            padding: 40px;
        }
        
        .demo-section {
            background: #f8f9fa;
            padding: 30px;
            border-radius: 15px;
            margin: 20px 0;
            border-left: 4px solid #4a8a4a;
        }
        
        .language-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }
        
        .language-card {
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(74, 138, 74, 0.1);
            text-align: center;
            transition: transform 0.3s ease;
        }
        
        .language-card:hover {
            transform: translateY(-5px);
        }
        
        .language-card .flag {
            font-size: 32px;
            margin-bottom: 10px;
        }
        
        .language-card .name {
            font-weight: 600;
            color: #4a8a4a;
            margin-bottom: 5px;
        }
        
        .language-card .code {
            color: #666;
            font-size: 14px;
        }
        
        .current-language {
            font-weight: bold;
            color: #4a8a4a;
            margin-bottom: 20px;
            font-size: 18px;
        }
        
        h1 {
            margin-bottom: 10px;
            font-size: 2.5rem;
        }
        
        .subtitle {
            opacity: 0.9;
            font-size: 1.1rem;
        }
        
        h2 {
            color: #4a8a4a;
            margin-bottom: 15px;
        }
        
        p {
            line-height: 1.6;
            margin-bottom: 15px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="language-selector" id="languageSelector">
                <button class="language-select-button" id="languageButton" aria-expanded="false" aria-haspopup="listbox">
                    <span class="language-flag">🇨🇳</span>
                    <span class="language-text">中文</span>
                    <span class="language-arrow">▼</span>
                </button>
                <ul class="language-dropdown" id="languageDropdown" role="listbox">
                    <li class="language-option" data-value="zh" role="option" aria-selected="true">
                        <span class="language-flag">🇨🇳</span>
                        <span class="language-text">中文</span>
                    </li>
                    <li class="language-option" data-value="ja" role="option" aria-selected="false">
                        <span class="language-flag">🇯🇵</span>
                        <span class="language-text">日本語</span>
                    </li>
                    <li class="language-option" data-value="en" role="option" aria-selected="false">
                        <span class="language-flag">🇺🇸</span>
                        <span class="language-text">English</span>
                    </li>
                    <li class="language-option" data-value="ko" role="option" aria-selected="false">
                        <span class="language-flag">🇰🇷</span>
                        <span class="language-text">한국어</span>
                    </li>
                    <li class="language-option" data-value="fr" role="option" aria-selected="false">
                        <span class="language-flag">🇫🇷</span>
                        <span class="language-text">Français</span>
                    </li>
                    <li class="language-option" data-value="de" role="option" aria-selected="false">
                        <span class="language-flag">🇩🇪</span>
                        <span class="language-text">Deutsch</span>
                    </li>
                    <li class="language-option" data-value="es" role="option" aria-selected="false">
                        <span class="language-flag">🇪🇸</span>
                        <span class="language-text">Español</span>
                    </li>
                    <li class="language-option" data-value="it" role="option" aria-selected="false">
                        <span class="language-flag">🇮🇹</span>
                        <span class="language-text">Italiano</span>
                    </li>
                    <li class="language-option" data-value="ru" role="option" aria-selected="false">
                        <span class="language-flag">🇷🇺</span>
                        <span class="language-text">Русский</span>
                    </li>
                    <li class="language-option" data-value="pt" role="option" aria-selected="false">
                        <span class="language-flag">🇵🇹</span>
                        <span class="language-text">Português</span>
                    </li>
                    <li class="language-option" data-value="th" role="option" aria-selected="false">
                        <span class="language-flag">🇹🇭</span>
                        <span class="language-text">ภาษาไทย</span>
                    </li>
                </ul>
            </div>
            <h1 id="pageTitle">多语言支持测试</h1>
            <p class="subtitle" id="pageSubtitle">Chiikawa Puzzle 现在支持 11 种语言</p>
        </div>
        
        <div class="content">
            <div class="current-language" id="currentLang">当前语言: 中文</div>
            
            <div class="demo-section">
                <h2 id="demoTitle">多语言功能演示</h2>
                <p id="demoText">这个演示展示了 Chiikawa Puzzle 游戏的多语言支持功能。点击右上角的语言选择器来切换不同的语言，体验完整的本地化界面。</p>
                
                <h2 id="featuresTitle">支持的语言:</h2>
                <div class="language-grid">
                    <div class="language-card">
                        <div class="flag">🇨🇳</div>
                        <div class="name">中文</div>
                        <div class="code">zh</div>
                    </div>
                    <div class="language-card">
                        <div class="flag">🇯🇵</div>
                        <div class="name">日本語</div>
                        <div class="code">ja</div>
                    </div>
                    <div class="language-card">
                        <div class="flag">🇺🇸</div>
                        <div class="name">English</div>
                        <div class="code">en</div>
                    </div>
                    <div class="language-card">
                        <div class="flag">🇰🇷</div>
                        <div class="name">한국어</div>
                        <div class="code">ko</div>
                    </div>
                    <div class="language-card">
                        <div class="flag">🇫🇷</div>
                        <div class="name">Français</div>
                        <div class="code">fr</div>
                    </div>
                    <div class="language-card">
                        <div class="flag">🇩🇪</div>
                        <div class="name">Deutsch</div>
                        <div class="code">de</div>
                    </div>
                    <div class="language-card">
                        <div class="flag">🇪🇸</div>
                        <div class="name">Español</div>
                        <div class="code">es</div>
                    </div>
                    <div class="language-card">
                        <div class="flag">🇮🇹</div>
                        <div class="name">Italiano</div>
                        <div class="code">it</div>
                    </div>
                    <div class="language-card">
                        <div class="flag">🇷🇺</div>
                        <div class="name">Русский</div>
                        <div class="code">ru</div>
                    </div>
                    <div class="language-card">
                        <div class="flag">🇵🇹</div>
                        <div class="name">Português</div>
                        <div class="code">pt</div>
                    </div>
                    <div class="language-card">
                        <div class="flag">🇹🇭</div>
                        <div class="name">ภาษาไทย</div>
                        <div class="code">th</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 简化的翻译数据用于测试
        const testTranslations = {
            zh: {
                pageTitle: '多语言支持测试',
                pageSubtitle: 'Chiikawa Puzzle 现在支持 11 种语言',
                currentLang: '当前语言: 中文',
                demoTitle: '多语言功能演示',
                demoText: '这个演示展示了 Chiikawa Puzzle 游戏的多语言支持功能。点击右上角的语言选择器来切换不同的语言，体验完整的本地化界面。',
                featuresTitle: '支持的语言:'
            },
            ja: {
                pageTitle: '多言語サポートテスト',
                pageSubtitle: 'Chiikawa Puzzle は現在11言語をサポートしています',
                currentLang: '現在の言語: 日本語',
                demoTitle: '多言語機能デモ',
                demoText: 'このデモは Chiikawa Puzzle ゲームの多言語サポート機能を示しています。右上の言語セレクターをクリックして異なる言語に切り替え、完全にローカライズされたインターフェースを体験してください。',
                featuresTitle: 'サポートされている言語:'
            },
            en: {
                pageTitle: 'Multilingual Support Test',
                pageSubtitle: 'Chiikawa Puzzle now supports 11 languages',
                currentLang: 'Current Language: English',
                demoTitle: 'Multilingual Feature Demo',
                demoText: 'This demo showcases the multilingual support feature of the Chiikawa Puzzle game. Click the language selector in the top right to switch between different languages and experience the fully localized interface.',
                featuresTitle: 'Supported Languages:'
            },
            ko: {
                pageTitle: '다국어 지원 테스트',
                pageSubtitle: 'Chiikawa Puzzle은 이제 11개 언어를 지원합니다',
                currentLang: '현재 언어: 한국어',
                demoTitle: '다국어 기능 데모',
                demoText: '이 데모는 Chiikawa Puzzle 게임의 다국어 지원 기능을 보여줍니다. 오른쪽 상단의 언어 선택기를 클릭하여 다른 언어로 전환하고 완전히 현지화된 인터페이스를 경험해보세요.',
                featuresTitle: '지원되는 언어:'
            },
            fr: {
                pageTitle: 'Test de Support Multilingue',
                pageSubtitle: 'Chiikawa Puzzle supporte maintenant 11 langues',
                currentLang: 'Langue Actuelle: Français',
                demoTitle: 'Démo de Fonctionnalité Multilingue',
                demoText: 'Cette démo présente la fonctionnalité de support multilingue du jeu Chiikawa Puzzle. Cliquez sur le sélecteur de langue en haut à droite pour basculer entre différentes langues et expérimenter l\'interface entièrement localisée.',
                featuresTitle: 'Langues Supportées:'
            },
            de: {
                pageTitle: 'Mehrsprachiger Support Test',
                pageSubtitle: 'Chiikawa Puzzle unterstützt jetzt 11 Sprachen',
                currentLang: 'Aktuelle Sprache: Deutsch',
                demoTitle: 'Mehrsprachige Funktions-Demo',
                demoText: 'Diese Demo zeigt die mehrsprachige Support-Funktion des Chiikawa Puzzle Spiels. Klicken Sie auf den Sprachselektor oben rechts, um zwischen verschiedenen Sprachen zu wechseln und die vollständig lokalisierte Benutzeroberfläche zu erleben.',
                featuresTitle: 'Unterstützte Sprachen:'
            },
            es: {
                pageTitle: 'Prueba de Soporte Multiidioma',
                pageSubtitle: 'Chiikawa Puzzle ahora soporta 11 idiomas',
                currentLang: 'Idioma Actual: Español',
                demoTitle: 'Demo de Funcionalidad Multiidioma',
                demoText: 'Esta demo muestra la funcionalidad de soporte multiidioma del juego Chiikawa Puzzle. Haz clic en el selector de idioma en la parte superior derecha para cambiar entre diferentes idiomas y experimentar la interfaz completamente localizada.',
                featuresTitle: 'Idiomas Soportados:'
            },
            it: {
                pageTitle: 'Test di Supporto Multilingue',
                pageSubtitle: 'Chiikawa Puzzle ora supporta 11 lingue',
                currentLang: 'Lingua Attuale: Italiano',
                demoTitle: 'Demo di Funzionalità Multilingue',
                demoText: 'Questa demo mostra la funzionalità di supporto multilingue del gioco Chiikawa Puzzle. Clicca sul selettore di lingua in alto a destra per passare tra diverse lingue e sperimentare l\'interfaccia completamente localizzata.',
                featuresTitle: 'Lingue Supportate:'
            },
            ru: {
                pageTitle: 'Тест Многоязычной Поддержки',
                pageSubtitle: 'Chiikawa Puzzle теперь поддерживает 11 языков',
                currentLang: 'Текущий Язык: Русский',
                demoTitle: 'Демо Многоязычной Функциональности',
                demoText: 'Эта демонстрация показывает функциональность многоязычной поддержки игры Chiikawa Puzzle. Нажмите на селектор языка в правом верхнем углу, чтобы переключаться между различными языками и испытать полностью локализованный интерфейс.',
                featuresTitle: 'Поддерживаемые Языки:'
            },
            pt: {
                pageTitle: 'Teste de Suporte Multilíngue',
                pageSubtitle: 'Chiikawa Puzzle agora suporta 11 idiomas',
                currentLang: 'Idioma Atual: Português',
                demoTitle: 'Demo de Funcionalidade Multilíngue',
                demoText: 'Esta demo mostra a funcionalidade de suporte multilíngue do jogo Chiikawa Puzzle. Clique no seletor de idioma no canto superior direito para alternar entre diferentes idiomas e experimentar a interface completamente localizada.',
                featuresTitle: 'Idiomas Suportados:'
            },
            th: {
                pageTitle: 'การทดสอบการรองรับหลายภาษา',
                pageSubtitle: 'Chiikawa Puzzle ตอนนี้รองรับ 11 ภาษา',
                currentLang: 'ภาษาปัจจุบัน: ภาษาไทย',
                demoTitle: 'การสาธิตฟีเจอร์หลายภาษา',
                demoText: 'การสาธิตนี้แสดงฟีเจอร์การรองรับหลายภาษาของเกม Chiikawa Puzzle คลิกตัวเลือกภาษาที่มุมขวาบนเพื่อเปลี่ยนระหว่างภาษาต่างๆ และสัมผัสอินเทอร์เฟซที่แปลเป็นภาษาท้องถิ่นอย่างสมบูรณ์',
                featuresTitle: 'ภาษาที่รองรับ:'
            }
        };

        // Language selector functionality
        document.addEventListener('DOMContentLoaded', function() {
            const languageSelector = document.getElementById('languageSelector');
            const languageButton = document.getElementById('languageButton');
            const languageDropdown = document.getElementById('languageDropdown');
            const languageOptions = document.querySelectorAll('.language-option');
            
            const languages = {
                zh: { flag: '🇨🇳', text: '中文' },
                ja: { flag: '🇯🇵', text: '日本語' },
                en: { flag: '🇺🇸', text: 'English' },
                ko: { flag: '🇰🇷', text: '한국어' },
                fr: { flag: '🇫🇷', text: 'Français' },
                de: { flag: '🇩🇪', text: 'Deutsch' },
                es: { flag: '🇪🇸', text: 'Español' },
                it: { flag: '🇮🇹', text: 'Italiano' },
                ru: { flag: '🇷🇺', text: 'Русский' },
                pt: { flag: '🇵🇹', text: 'Português' },
                th: { flag: '🇹🇭', text: 'ภาษาไทย' }
            };
            
            let isDropdownOpen = false;
            let currentLanguage = 'zh';
            
            function updateContent(lang) {
                const t = testTranslations[lang];
                document.getElementById('pageTitle').textContent = t.pageTitle;
                document.getElementById('pageSubtitle').textContent = t.pageSubtitle;
                document.getElementById('currentLang').textContent = t.currentLang;
                document.getElementById('demoTitle').textContent = t.demoTitle;
                document.getElementById('demoText').textContent = t.demoText;
                document.getElementById('featuresTitle').textContent = t.featuresTitle;
            }
            
            function toggleDropdown() {
                isDropdownOpen = !isDropdownOpen;
                languageDropdown.classList.toggle('show', isDropdownOpen);
                languageButton.setAttribute('aria-expanded', isDropdownOpen);
            }
            
            function closeDropdown() {
                isDropdownOpen = false;
                languageDropdown.classList.remove('show');
                languageButton.setAttribute('aria-expanded', 'false');
            }
            
            function updateLanguageButton(langCode) {
                const lang = languages[langCode];
                if (lang) {
                    languageButton.querySelector('.language-flag').textContent = lang.flag;
                    languageButton.querySelector('.language-text').textContent = lang.text;
                }
            }
            
            function updateLanguageOptions(selectedLang) {
                languageOptions.forEach(option => {
                    const isSelected = option.dataset.value === selectedLang;
                    option.setAttribute('aria-selected', isSelected);
                });
            }
            
            function changeLanguage(newLang) {
                currentLanguage = newLang;
                updateLanguageButton(newLang);
                updateLanguageOptions(newLang);
                updateContent(newLang);
                closeDropdown();
            }
            
            languageButton.addEventListener('click', toggleDropdown);
            
            languageOptions.forEach(option => {
                option.addEventListener('click', function() {
                    changeLanguage(this.dataset.value);
                });
            });
            
            document.addEventListener('click', function(e) {
                if (!languageSelector.contains(e.target)) {
                    closeDropdown();
                }
            });
        });
    </script>
</body>
</html>
