* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: linear-gradient(135deg, #e8f5e8 0%, #f0f9f0 50%, #e1f4e1 100%);
    margin: 0;
    padding: 0;
    color: #2d5a2d;
    line-height: 1.6;
    overflow-x: hidden;
    min-height: 100vh;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 24px;
}

/* Header Styles */
header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    padding: 20px 0;
    box-shadow: 0 4px 30px rgba(45, 90, 45, 0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
    border-bottom: 2px solid #a8d5a8;
    margin-bottom: 0;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.header-controls {
    display: flex;
    align-items: center;
    gap: 16px;
}

.logo-container {
    display: flex;
    align-items: center;
    gap: 16px;
}

/* Language Selector Container */
.language-selector {
    position: relative;
    display: inline-block;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Hidden select for compatibility */
.language-select-hidden {
    position: absolute;
    opacity: 0;
    pointer-events: none;
    z-index: -1;
}

/* Custom Language Select Button */
.language-select-button {
    background: linear-gradient(135deg, #4a8a4a, #5a9a5a);
    color: #ffffff;
    border: 2px solid rgba(255, 255, 255, 0.3);
    padding: 10px 16px;
    border-radius: 25px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 4px 12px rgba(74, 138, 74, 0.25);
    outline: none;
    min-width: 120px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 8px;
    backdrop-filter: blur(10px);
    letter-spacing: 0.5px;
    position: relative;
    overflow: hidden;
}

/* Subtle pulse animation on first load */
.language-select-button::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(135deg, #4a8a4a, #5a9a5a);
    border-radius: 27px;
    z-index: -1;
    opacity: 0;
    animation: languagePulse 3s ease-in-out infinite;
}

@keyframes languagePulse {
    0%, 70%, 100% {
        opacity: 0;
        transform: scale(1);
    }
    35% {
        opacity: 0.3;
        transform: scale(1.05);
    }
}

/* Language flag styling */
.language-flag {
    font-size: 16px;
    line-height: 1;
    flex-shrink: 0;
}

/* Language text styling */
.language-text {
    flex: 1;
    text-align: center;
    white-space: nowrap;
}

/* Language arrow styling */
.language-arrow {
    font-size: 10px;
    transition: transform 0.3s ease;
    flex-shrink: 0;
}

/* Button states */
.language-select-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(74, 138, 74, 0.35);
    background: linear-gradient(135deg, #5a9a5a, #6aaa6a);
    border-color: rgba(255, 255, 255, 0.5);
}

.language-select-button:hover .language-arrow {
    transform: rotate(180deg);
}

.language-select-button:focus {
    transform: translateY(-1px);
    box-shadow: 0 0 0 3px rgba(74, 138, 74, 0.3), 0 6px 20px rgba(74, 138, 74, 0.35);
    background: linear-gradient(135deg, #5a9a5a, #6aaa6a);
}

.language-select-button:active {
    transform: translateY(0);
    box-shadow: 0 2px 8px rgba(74, 138, 74, 0.4);
}

.language-select-button[aria-expanded="true"] .language-arrow {
    transform: rotate(180deg);
}

/* Language Dropdown */
.language-dropdown {
    position: absolute;
    top: calc(100% + 8px);
    left: 0;
    right: 0;
    background: rgba(255, 255, 255, 0.95);
    border: 2px solid rgba(74, 138, 74, 0.2);
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(74, 138, 74, 0.2);
    backdrop-filter: blur(20px);
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px) scale(0.95);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    list-style: none;
    margin: 0;
    padding: 8px;
    overflow: hidden;
    max-height: 0;
}

.language-dropdown.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0) scale(1);
    max-height: 200px;
    animation: dropdownSlide 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes dropdownSlide {
    0% {
        opacity: 0;
        transform: translateY(-10px) scale(0.95);
        max-height: 0;
    }
    50% {
        opacity: 0.7;
        transform: translateY(-2px) scale(0.98);
    }
    100% {
        opacity: 1;
        transform: translateY(0) scale(1);
        max-height: 200px;
    }
}

/* Language Option */
.language-option {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 16px;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    color: #2d5a2d;
    font-weight: 500;
    font-size: 14px;
    position: relative;
    overflow: hidden;
}

.language-option::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(74, 138, 74, 0.1), transparent);
    transition: left 0.5s ease;
}

.language-option:hover::before {
    left: 100%;
}

.language-option:hover {
    background: linear-gradient(135deg, #f0f8f0, #e8f5e8);
    transform: translateX(4px);
    box-shadow: 0 2px 8px rgba(74, 138, 74, 0.15);
}

.language-option[aria-selected="true"] {
    background: linear-gradient(135deg, #4a8a4a, #5a9a5a);
    color: #ffffff;
    font-weight: 600;
    box-shadow: 0 2px 8px rgba(74, 138, 74, 0.3);
}

.language-option[aria-selected="true"]:hover {
    background: linear-gradient(135deg, #5a9a5a, #6aaa6a);
    transform: translateX(2px);
}

/* Focus states for accessibility */
.language-select-button:focus-visible {
    outline: 2px solid #4a8a4a;
    outline-offset: 2px;
}

.language-option:focus {
    outline: 2px solid #4a8a4a;
    outline-offset: -2px;
}

/* Keyboard navigation support */
.language-select-button:focus:not(:focus-visible) {
    outline: none;
}

.mobile-menu-toggle {
    display: none;
    flex-direction: column;
    background: none;
    border: none;
    cursor: pointer;
    padding: 8px;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.mobile-menu-toggle span {
    width: 25px;
    height: 3px;
    background: #2d5a2d;
    margin: 3px 0;
    transition: all 0.3s ease;
    border-radius: 2px;
}

.mobile-menu-toggle:hover {
    background: rgba(45, 90, 45, 0.1);
}

.mobile-menu-toggle.active span:nth-child(1) {
    transform: rotate(45deg) translate(5px, 5px);
}

.mobile-menu-toggle.active span:nth-child(2) {
    opacity: 0;
}

.mobile-menu-toggle.active span:nth-child(3) {
    transform: rotate(-45deg) translate(7px, -6px);
}

.logo {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(45, 90, 45, 0.2);
    transition: all 0.3s ease;
}

.logo:hover {
    transform: scale(1.1) rotate(5deg);
    box-shadow: 0 6px 25px rgba(45, 90, 45, 0.3);
}

header h1 {
    font-family: 'Fredoka One', sans-serif;
    color: #2d5a2d;
    font-size: 2.8rem;
    margin: 0;
    font-weight: 400;
    letter-spacing: -0.01em;
    text-shadow: 2px 2px 4px rgba(45, 90, 45, 0.1);
}

header nav ul {
    display: flex;
    justify-content: center;
    list-style: none;
    gap: 40px;
    margin: 0;
    padding: 0;
}

@media (min-width: 901px) {
    .header-content {
        justify-content: center;
    }
    
    .mobile-menu-toggle {
        display: none !important;
    }
    
    .nav-menu {
        display: block !important;
    }
}

header nav ul li {
    position: relative;
}

header nav ul li a {
    text-decoration: none;
    color: #4a7c4a;
    font-size: 1.1rem;
    font-weight: 600;
    padding: 12px 20px;
    border-radius: 25px;
    transition: all 0.3s ease;
    position: relative;
    background: linear-gradient(135deg, #c8e6c8, #b8ddb8);
    border: 2px solid transparent;
}

header nav ul li a:hover {
    color: #ffffff;
    background: linear-gradient(135deg, #5a9a5a, #4a8a4a);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(45, 90, 45, 0.3);
}

/* Main Content Styles */
main {
    padding: 0;
}

/* Hero Section */
.hero {
    text-align: center;
    padding: 60px 0 100px;
    background: linear-gradient(135deg, #4a8a4a 0%, #5a9a5a 50%, #6aaa6a 100%);
    color: white;
    margin-bottom: 0;
    position: relative;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="40" r="1.5" fill="rgba(255,255,255,0.08)"/><circle cx="40" cy="70" r="1" fill="rgba(255,255,255,0.06)"/><circle cx="70" cy="80" r="2.5" fill="rgba(255,255,255,0.1)"/></svg>') repeat;
    animation: float 20s infinite linear;
}

@keyframes float {
    0% { transform: translateY(0px); }
    100% { transform: translateY(-100px); }
}

.hero h2 {
    font-size: 4rem;
    font-weight: 700;
    margin-top: 0;
    margin-bottom: 30px;
    letter-spacing: -0.02em;
    text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.3);
    position: relative;
    z-index: 1;
}

.hero p {
    font-size: 1.4rem;
    max-width: 700px;
    margin: 0 auto 50px;
    line-height: 1.7;
    opacity: 0.95;
    text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.2);
    position: relative;
    z-index: 1;
}

.cta-button {
    display: inline-block;
    background: linear-gradient(135deg, #ffffff 0%, #f8fff8 100%);
    color: #2d5a2d;
    padding: 18px 40px;
    border-radius: 50px;
    text-decoration: none;
    font-weight: 700;
    font-size: 1.2rem;
    transition: all 0.4s ease;
    box-shadow: 0 10px 35px rgba(0, 0, 0, 0.2);
    border: 3px solid rgba(255, 255, 255, 0.3);
    position: relative;
    z-index: 2;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.cta-button:hover {
    transform: translateY(-4px) scale(1.05);
    box-shadow: 0 15px 50px rgba(0, 0, 0, 0.3);
    background: linear-gradient(135deg, #e8f5e8 0%, #d8eed8 100%);
    border-color: rgba(255, 255, 255, 0.6);
}

#game-intro {
    text-align: center;
    margin: 60px 0 80px;
    padding: 60px 0;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 20px;
    box-shadow: 0 8px 40px rgba(45, 90, 45, 0.1);
    backdrop-filter: blur(10px);
}

#game-intro h2 {
    font-size: 3rem;
    color: #2d5a2d;
    margin-bottom: 30px;
    font-weight: 700;
    letter-spacing: -0.01em;
    text-shadow: 2px 2px 4px rgba(45, 90, 45, 0.1);
}

#game-intro p {
    font-size: 1.2rem;
    max-width: 800px;
    margin: 0 auto 25px;
    line-height: 1.8;
    color: #4a7c4a;
    font-weight: 500;
}

#game-container {
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 60px 0;
    padding: 40px;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 255, 248, 0.9) 100%);
    border-radius: 25px;
    border: 3px solid #a8d5a8;
    box-shadow: 0 12px 50px rgba(45, 90, 45, 0.15);
    backdrop-filter: blur(15px);
}

#game-container canvas {
    border: none;
    border-radius: 12px;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
    background-color: white;
    transition: transform 0.2s ease;
}

#game-container canvas:hover {
    transform: scale(1.01);
}

/* Section Styles */
section {
    margin: 100px 0;
    padding: 0;
    background: none;
    border: none;
    box-shadow: none;
}

section h2 {
    text-align: center;
    font-size: 3rem;
    color: #2d5a2d;
    margin-bottom: 60px;
    font-weight: 700;
    letter-spacing: -0.01em;
    text-shadow: 2px 2px 4px rgba(45, 90, 45, 0.1);
}

/* Game Info Section */
#game-info {
    background: linear-gradient(135deg, rgba(168, 213, 168, 0.3) 0%, rgba(200, 230, 200, 0.3) 100%);
    padding: 100px 0;
    margin: 0;
    border-radius: 30px;
    backdrop-filter: blur(10px);
}

#game-info .info-content {
    max-width: 900px;
    margin: 0 auto;
    text-align: center;
}

#game-info p {
    font-size: 1.2rem;
    line-height: 1.8;
    margin-bottom: 30px;
    color: #4a7c4a;
    text-align: left;
    font-weight: 500;
}

/* How to Play Section */
#how-to-play {
    padding: 100px 0;
    background: rgba(255, 255, 255, 0.6);
    backdrop-filter: blur(10px);
    border-radius: 30px;
}

.steps {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 40px;
    margin-top: 60px;
}

.step {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 255, 248, 0.9) 100%);
    padding: 40px 30px;
    border-radius: 25px;
    border: 3px solid #a8d5a8;
    box-shadow: 0 8px 35px rgba(45, 90, 45, 0.15);
    text-align: center;
    transition: all 0.4s ease;
    position: relative;
    backdrop-filter: blur(15px);
}

.step:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 15px 50px rgba(45, 90, 45, 0.25);
    border-color: #5a9a5a;
}

.step h3 {
    color: #2d5a2d;
    margin-bottom: 20px;
    font-size: 1.4rem;
    font-weight: 700;
    text-shadow: 1px 1px 2px rgba(45, 90, 45, 0.1);
}

.step p {
    color: #4a7c4a;
    font-size: 1.1rem;
    line-height: 1.7;
    font-weight: 500;
}

/* Features Section */
#features {
    padding: 100px 0;
    background: linear-gradient(135deg, rgba(168, 213, 168, 0.2) 0%, rgba(200, 230, 200, 0.2) 100%);
    border-radius: 30px;
    backdrop-filter: blur(10px);
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 40px;
    margin-top: 60px;
}

.feature {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 255, 248, 0.9) 100%);
    padding: 40px 30px;
    border-radius: 25px;
    border: 3px solid #a8d5a8;
    box-shadow: 0 8px 35px rgba(45, 90, 45, 0.15);
    text-align: center;
    transition: all 0.4s ease;
    position: relative;
    backdrop-filter: blur(15px);
}

.feature:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 15px 50px rgba(45, 90, 45, 0.25);
    border-color: #5a9a5a;
}

.feature h3 {
    color: #2d5a2d;
    margin-bottom: 20px;
    font-size: 1.4rem;
    font-weight: 700;
    text-shadow: 1px 1px 2px rgba(45, 90, 45, 0.1);
}

.feature p {
    color: #4a7c4a;
    font-size: 1.1rem;
    line-height: 1.7;
    font-weight: 500;
}

/* Footer Styles */
footer {
    background: linear-gradient(135deg, #2d5a2d 0%, #1a4a1a 100%);
    color: white;
    text-align: center;
    padding: 80px 0;
    margin-top: 0;
    position: relative;
    overflow: hidden;
}

footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="10" cy="10" r="1" fill="rgba(255,255,255,0.05)"/><circle cx="90" cy="30" r="1.5" fill="rgba(255,255,255,0.03)"/><circle cx="30" cy="80" r="0.8" fill="rgba(255,255,255,0.04)"/><circle cx="80" cy="70" r="2" fill="rgba(255,255,255,0.06)"/></svg>') repeat;
    animation: float 25s infinite linear;
}

footer p {
    margin: 15px 0;
    font-size: 1rem;
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.7;
    position: relative;
    z-index: 1;
}

/* Button Styles */
.button {
    background: linear-gradient(135deg, #5a9a5a 0%, #4a8a4a 100%);
    border: none;
    color: white;
    padding: 16px 32px;
    text-align: center;
    text-decoration: none;
    display: inline-block;
    font-size: 1.1rem;
    font-weight: 700;
    margin: 10px 6px;
    cursor: pointer;
    border-radius: 25px;
    transition: all 0.4s ease;
    box-shadow: 0 6px 25px rgba(90, 154, 90, 0.4);
    border: 2px solid rgba(255, 255, 255, 0.2);
    text-transform: uppercase;
    letter-spacing: 1px;
}

.button:hover {
    background: linear-gradient(135deg, #6aaa6a 0%, #5a9a5a 100%);
    transform: translateY(-3px) scale(1.05);
    box-shadow: 0 10px 35px rgba(90, 154, 90, 0.6);
    border-color: rgba(255, 255, 255, 0.4);
}

.button:active {
    transform: translateY(-1px) scale(1.02);
}

.loading-text {
    font-size: 1.8rem;
    color: #2d5a2d;
    text-align: center;
    font-weight: 700;
    text-shadow: 2px 2px 4px rgba(45, 90, 45, 0.1);
    background: linear-gradient(135deg, #2d5a2d 0%, #4a7c4a 50%, #5a9a5a 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .container {
        padding: 0 20px;
    }
}

@media (max-width: 900px) {
    .mobile-menu-toggle {
        display: flex;
    }

    .header-content {
        margin-bottom: 0;
    }

    /* Mobile language selector adjustments */
    .language-select-button {
        min-width: 100px;
        padding: 8px 12px;
        font-size: 13px;
        border-radius: 20px;
        gap: 6px;
    }

    .language-flag {
        font-size: 14px;
    }

    .language-arrow {
        font-size: 9px;
    }

    .language-dropdown {
        border-radius: 12px;
        padding: 6px;
    }

    .language-option {
        padding: 10px 12px;
        gap: 10px;
        font-size: 13px;
        border-radius: 10px;
    }
    
    .nav-menu {
        display: none;
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: rgba(255, 255, 255, 0.98);
        backdrop-filter: blur(15px);
        box-shadow: 0 8px 32px rgba(45, 90, 45, 0.15);
        border-top: 1px solid #a8d5a8;
        padding: 20px 0;
    }
    
    .nav-menu.active {
        display: block;
        animation: slideDown 0.3s ease;
    }
    
    .nav-menu ul {
        flex-direction: column;
        align-items: center;
        gap: 16px;
        margin: 0;
        padding: 0;
    }
    
    .nav-menu ul li a {
        display: block;
        padding: 12px 24px;
        width: 200px;
        text-align: center;
        border-radius: 12px;
        font-size: 1rem;
    }
    
    header h1 {
        font-size: 2rem;
    }
    
    .hero h2 {
        font-size: 2.8rem;
    }
    
    .hero p {
        font-size: 1.1rem;
    }
    
    #game-intro h2 {
        font-size: 2rem;
    }
    
    section h2 {
        font-size: 2rem;
    }
    
    .steps {
        grid-template-columns: 1fr;
        gap: 24px;
    }
    
    .features-grid {
        grid-template-columns: 1fr;
        gap: 24px;
    }
    
    #game-container {
        padding: 24px;
    }
    
    #game-container canvas {
        max-width: 90vw;
        max-height: 65vh;
    }
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@media (max-width: 600px) {
    header {
        padding: 16px 0;
    }
    
    header h1 {
        font-size: 1.6rem;
        margin-bottom: 12px;
    }
    
    .hero {
        padding: 60px 0 80px;
    }
    
    .hero h2 {
        font-size: 2.2rem;
    }
    
    .hero p {
        font-size: 1rem;
    }
    
    .cta-button {
        padding: 12px 24px;
        font-size: 1rem;
    }
    
    main {
        padding: 60px 0;
    }
    
    section {
        margin: 60px 0;
        padding: 60px 0;
    }
    
    #game-intro {
        margin-bottom: 60px;
    }
    
    #game-intro h2 {
        font-size: 1.8rem;
        margin-bottom: 20px;
    }
    
    #game-intro p {
        font-size: 1rem;
        line-height: 1.6;
    }
    
    section h2 {
        font-size: 1.8rem;
        margin-bottom: 32px;
    }
    
    .step, .feature {
        padding: 24px 20px;
    }
    
    .step h3, .feature h3 {
        font-size: 1.2rem;
    }
    
    .step p, .feature p {
        font-size: 0.95rem;
    }
    
    .button {
        padding: 12px 24px;
        font-size: 0.95rem;
    }
    
    #game-container {
        padding: 20px;
        margin: 40px 0;
    }
    
    #game-container canvas {
        max-width: 85vw;
        max-height: 55vh;
    }
    
    footer {
        padding: 40px 0;
        margin-top: 60px;
    }
    
    footer p {
        font-size: 0.9rem;
        margin: 8px 0;
    }
}

@media (max-width: 480px) {
    .hero h2 {
        font-size: 1.8rem;
    }

    #game-intro h2 {
        font-size: 1.6rem;
    }

    section h2 {
        font-size: 1.6rem;
    }

    #game-container canvas {
        max-width: 80vw;
        max-height: 50vh;
    }

    /* Extra small screen language selector */
    .language-select-button {
        min-width: 85px;
        padding: 6px 10px;
        font-size: 12px;
        border-radius: 18px;
        gap: 4px;
    }

    .language-flag {
        font-size: 12px;
    }

    .language-arrow {
        font-size: 8px;
    }

    .language-dropdown {
        border-radius: 10px;
        padding: 4px;
    }

    .language-option {
        padding: 8px 10px;
        gap: 8px;
        font-size: 12px;
        border-radius: 8px;
    }
}

@media (max-height: 700px) {
    #game-container canvas {
        max-height: 50vh;
    }
}

@media (max-height: 500px) {
    #game-container canvas {
        max-height: 40vh;
    }
}

/* Animation for smooth scrolling */
html {
    scroll-behavior: smooth;
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(168, 213, 168, 0.2);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #5a9a5a 0%, #4a8a4a 100%);
    border-radius: 4px;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #6aaa6a 0%, #5a9a5a 100%);
    transform: scale(1.1);
}