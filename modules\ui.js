// UI Module - Handles all UI components like buttons and text
class UIManager {
    constructor(app) {
        this.app = app;
        this.uiContainer = new PIXI.Container();
        app.stage.addChild(this.uiContainer);
    }

    createIconButtonWithImage(iconType, x, y, callback, scale = 1) {
        // Create container for the button
        const button = new PIXI.Container();
        
        // Load the appropriate icon texture
        let iconTexturePath = '';
        if (iconType === 'start') {
            iconTexturePath = 'assets/icon/start_game_icon.png';
        } else if (iconType === 'restart') {
            iconTexturePath = 'assets/icon/replay_icon.png';
        } else if (iconType === 'download') {
            iconTexturePath = 'assets/icon/share_icon.png';
        }
        
        // Create sprite from the icon texture
        const iconSprite = GameUtils.createImageSprite(iconTexturePath);
        
        if (iconSprite) {
            // Center the icon
            iconSprite.anchor.set(0.5);
            
            // For start button, we need a larger area
            if (iconType === 'start') {
                // Add invisible background for better click area
                const invisibleBg = new PIXI.Graphics();
                invisibleBg.beginFill(0x000000, 0.01); // Almost transparent
                invisibleBg.drawRoundedRect(0, 0, 200 * scale, 60 * scale, 8 * scale);
                invisibleBg.endFill();
                button.addChild(invisibleBg);
                
                iconSprite.x = 100 * scale;
                iconSprite.y = 30 * scale;
                // Scale the icon sprite
                iconSprite.scale.set(scale);
            } else {
                // For other icons
                // Add invisible background for better click area
                const invisibleBg = new PIXI.Graphics();
                invisibleBg.beginFill(0x000000, 0.01); // Almost transparent
                invisibleBg.drawRect(0, 0, 60 * scale, 60 * scale);
                invisibleBg.endFill();
                button.addChild(invisibleBg);
                
                iconSprite.x = 30 * scale;
                iconSprite.y = 30 * scale;
                // Scale the icon sprite
                iconSprite.scale.set(scale);
            }
            
            // Add icon to button
            button.addChild(iconSprite);
        } else {
            // Fallback to original drawing if image fails to load
            if (iconType === 'start') {
                const buttonBg = new PIXI.Graphics();
                buttonBg.beginFill(0x4CAF50);
                buttonBg.drawRoundedRect(0, 0, 200 * scale, 60 * scale, 8 * scale);
                buttonBg.endFill();
                
                const buttonText = new PIXI.Text('Start Game', {
                    fontFamily: 'Arial',
                    fontSize: 18 * scale,
                    fill: 0xFFFFFF,
                    align: 'center'
                });
                buttonText.anchor.set(0.5);
                buttonText.x = 100 * scale;
                buttonText.y = 30 * scale;
                
                button.addChild(buttonBg);
                button.addChild(buttonText);
            } else {
                const buttonBg = new PIXI.Graphics();
                buttonBg.beginFill(0x4CAF50);
                buttonBg.drawRect(0, 0, 60 * scale, 60 * scale);
                buttonBg.endFill();
                
                const icon = new PIXI.Graphics();
                icon.lineStyle(3 * scale, 0xFFFFFF);
                
                if (iconType === 'restart') {
                    // Restart icon - circular arrow
                    icon.arc(30 * scale, 30 * scale, 12 * scale, 0, Math.PI * 1.8);
                    icon.moveTo(42 * scale, 18 * scale);
                    icon.lineTo(36 * scale, 15 * scale);
                    icon.lineTo(36 * scale, 21 * scale);
                } else if (iconType === 'download') {
                    // Download icon - arrow down with line
                    icon.moveTo(30 * scale, 15 * scale);
                    icon.lineTo(30 * scale, 35 * scale);
                    icon.moveTo(22 * scale, 27 * scale);
                    icon.lineTo(30 * scale, 35 * scale);
                    icon.lineTo(38 * scale, 27 * scale);
                    icon.moveTo(18 * scale, 42 * scale);
                    icon.lineTo(42 * scale, 42 * scale);
                }
                
                button.addChild(buttonBg);
                button.addChild(icon);
            }
        }
        
        // Position the button
        if (iconType === 'start') {
            button.x = x - (100 * scale);
            button.y = y - (30 * scale);
        } else {
            button.x = x - (30 * scale);
            button.y = y - (30 * scale);
        }
        
        button.interactive = true;
        button.buttonMode = true;
        
        // Add hover and click effects (no need to scale the entire button)
        button.on('pointerover', () => {
            // Only scale the contents, not the entire button
            if (iconSprite) {
                iconSprite.scale.set(scale * 1.1);
            }
        });
        
        button.on('pointerout', () => {
            // Only scale the contents, not the entire button
            if (iconSprite) {
                iconSprite.scale.set(scale);
            }
        });
        
        button.on('pointerdown', () => {
            // Only scale the contents, not the entire button
            if (iconSprite) {
                iconSprite.scale.set(scale * 0.95);
            }
        });
        
        button.on('pointerup', () => {
            // Only scale the contents, not the entire button
            if (iconSprite) {
                iconSprite.scale.set(scale * 1.1);
            }
            callback();
        });
        
        return button;
    }

    createIconButton(iconType, x, y, callback, scale = 1) {
        // Create container for the button
        const button = new PIXI.Container();
        
        // Load the appropriate icon texture
        let iconTexturePath = '';
        if (iconType === 'restart') {
            iconTexturePath = 'assets/icon/replay_icon.png';
        } else if (iconType === 'download') {
            iconTexturePath = 'assets/icon/share_icon.png';
        }
        
        // Create sprite from the icon texture
        const iconSprite = GameUtils.createImageSprite(iconTexturePath);
        
        if (iconSprite) {
            // Center the icon
            iconSprite.anchor.set(0.5);
            iconSprite.x = 30 * scale;
            iconSprite.y = 30 * scale;
            
            // Scale the icon sprite
            iconSprite.scale.set(scale);
            
            // Add invisible background for better click area
            const invisibleBg = new PIXI.Graphics();
            invisibleBg.beginFill(0x000000, 0.01); // Almost transparent
            invisibleBg.drawRect(0, 0, 60 * scale, 60 * scale);
            invisibleBg.endFill();
            button.addChild(invisibleBg);
            
            // Add icon to button
            button.addChild(iconSprite);
        } else {
            // Fallback to original drawing if image fails to load
            const buttonBg = new PIXI.Graphics();
            buttonBg.beginFill(0x4CAF50);
            buttonBg.drawRect(0, 0, 60 * scale, 60 * scale);
            buttonBg.endFill();
            
            const icon = new PIXI.Graphics();
            icon.lineStyle(3 * scale, 0xFFFFFF);
            
            if (iconType === 'restart') {
                // Restart icon - circular arrow
                icon.arc(30 * scale, 30 * scale, 12 * scale, 0, Math.PI * 1.8);
                icon.moveTo(42 * scale, 18 * scale);
                icon.lineTo(36 * scale, 15 * scale);
                icon.lineTo(36 * scale, 21 * scale);
            } else if (iconType === 'download') {
                // Download icon - arrow down with line
                icon.moveTo(30 * scale, 15 * scale);
                icon.lineTo(30 * scale, 35 * scale);
                icon.moveTo(22 * scale, 27 * scale);
                icon.lineTo(30 * scale, 35 * scale);
                icon.lineTo(38 * scale, 27 * scale);
                icon.moveTo(18 * scale, 42 * scale);
                icon.lineTo(42 * scale, 42 * scale);
            }
            
            button.addChild(buttonBg);
            button.addChild(icon);
        }
        
        button.x = x - (30 * scale);
        button.y = y - (30 * scale);
        button.interactive = true;
        button.buttonMode = true;
        
        // Add hover and click effects (no need to scale the entire button)
        button.on('pointerover', () => {
            // Only scale the contents, not the entire button
            if (iconSprite) {
                iconSprite.scale.set(scale * 1.1);
            }
        });
        
        button.on('pointerout', () => {
            // Only scale the contents, not the entire button
            if (iconSprite) {
                iconSprite.scale.set(scale);
            }
        });
        
        button.on('pointerdown', () => {
            // Only scale the contents, not the entire button
            if (iconSprite) {
                iconSprite.scale.set(scale * 0.95);
            }
        });
        
        button.on('pointerup', () => {
            // Only scale the contents, not the entire button
            if (iconSprite) {
                iconSprite.scale.set(scale * 1.1);
            }
            callback();
        });
        
        return button;
    }

    createText(text, options = {}) {
        const defaultOptions = {
            fontFamily: 'Arial',
            fontSize: 24,
            fill: 0x333333,
            align: 'center'
        };
        
        return new PIXI.Text(text, { ...defaultOptions, ...options });
    }

    addToUI(element) {
        this.uiContainer.addChild(element);
    }

    removeFromUI(element) {
        this.uiContainer.removeChild(element);
    }

    clearUI() {
        this.uiContainer.removeChildren();
    }

    fadeInElement(element, speed = 0.05) {
        element.alpha = 0;
        const fadeIn = () => {
            element.alpha += speed;
            if (element.alpha < 1) {
                requestAnimationFrame(fadeIn);
            }
        };
        fadeIn();
    }

    fadeOutElement(element, speed = 0.1, callback = null) {
        const fadeOut = () => {
            element.alpha -= speed;
            if (element.alpha <= 0) {
                element.alpha = 0;
                if (callback) callback();
            } else {
                requestAnimationFrame(fadeOut);
            }
        };
        fadeOut();
    }

    // Create tooltip for buttons
    createTooltip(text, x, y) {
        // Create tooltip container
        const tooltip = new PIXI.Container();
        
        // Create tooltip text
        const tooltipText = new PIXI.Text(text, {
            fontFamily: 'Arial',
            fontSize: 14,
            fill: 0xFFFFFF,
            align: 'center',
            fontWeight: 'bold'
        });
        tooltipText.anchor.set(0.5);
        
        // Create tooltip background
        const background = new PIXI.Graphics();
        background.beginFill(0x333333, 0.9);
        background.drawRoundedRect(
            -tooltipText.width / 2 - 10, 
            -tooltipText.height / 2 - 5, 
            tooltipText.width + 20, 
            tooltipText.height + 10, 
            5
        );
        background.endFill();
        
        // Add background and text to tooltip
        tooltip.addChild(background);
        tooltip.addChild(tooltipText);
        
        // Position tooltip (move it higher to avoid overlapping with button)
        tooltip.x = x;
        tooltip.y = y - 70; // Move tooltip higher (50 + 20px)
        
        // Initially hide tooltip
        tooltip.alpha = 0;
        
        // Store reference to background for positioning adjustments
        tooltip.background = background;
        tooltip.tooltipText = tooltipText;
        tooltip.baseX = x; // Store original x position
        
        return tooltip;
    }
}

window.UIManager = UIManager;