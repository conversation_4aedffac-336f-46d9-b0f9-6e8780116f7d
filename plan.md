Product Requirements Document (PRD): Chiikawa PuzzleVersion: 1.0Date: August 16, 2025Product Name: Chiikawa Puzzle1. Product Overview1.1. ConceptA casual, humor-driven memory puzzle game delivered as a web application. The player is briefly shown a complete image of the character <PERSON><PERSON><PERSON>, then must reassemble the character from its constituent parts based on memory. The core engagement loop is driven by the comedic, often absurd, results of imperfect memory, creating highly shareable content.1.2. Target AudienceFans of the Chiikawa (ちいかわ) franchise.Casual gamers seeking quick, engaging, and shareable web-based experiences.1.3. Core Objectives & Scope (MVP)Develop a simple, single-level game loop with one character (<PERSON><PERSON><PERSON>).Ensure the user experience is intuitive and polished with smooth transitions and clear feedback.Implement a "Share" feature that encourages viral distribution of user-generated results.2. Game Flow & MechanicsThe game is architected as a finite state machine with four primary states: START_SCREEN, MEMORY, PUZZLING, and REVEAL.2.1. State: START_SCREENUI Elements:Game Title: "Chiikawa Puzzle" displayed centrally.Start Button: A prominent button below the title with the label "Start Game".States: The button must have distinct visual states for hover (e.g., slight increase in brightness or size) and active (e.g., slightly inset or darker).Background: A static, soft, solid color (e.g., cream #FDF6E3).Interaction Logic:On user click of the "Start Game" button, the application transitions to the MEMORY state.2.2. State: MEMORYUI Elements:Reference Image: The complete, correctly assembled Chiikawa character sprite is displayed in the center of the canvas.Countdown Timer: A large, clear text element is displayed above the image, formatted as "Memorize This! 5". The number decrements every second.Interaction Logic:User input is disabled during this state.The timer runs for 5 seconds (5, 4, 3, 2, 1).After the timer reaches zero, both the reference image and the timer text fade out smoothly over 300ms (ease-out timing function).Upon completion of the fade-out animation, the application transitions to the PUZZLING state.2.3. State: PUZZLINGUI Elements:Canvas: The main canvas is cleared to a blank white background.Instructional Text: A text element at the top of the canvas reads "Place the: [Current Part Name]" (e.g., "Place the: Head").Cursor Sprite: The sprite for the current puzzle part to be placed is rendered on the canvas. Its center point is locked to the user's mouse cursor position. The default system cursor should be hidden (cursor: none).Interaction Logic:The cursor sprite follows the mouse smoothly within the canvas bounds.On a left mouse click:A static copy of the current cursor sprite is rendered onto a dedicated "placed parts" container at the exact click location.The cursor sprite's texture is immediately updated to the next part in the sequence. If it's the first part being placed, the sprite should fade in over 150ms.The instructional text updates to the name of the new part.This process repeats until all parts from the predefined list have been placed.After the final part is placed, there is a 1-second pause (the "pre-reveal pause") before transitioning to the REVEAL state.2.4. State: REVEALUI Elements:Layout: The canvas is visually split into two main areas.Player Creation Area (Left 60%): Displays the final composition of all parts placed by the player.Reference Image Area (Right 40%): Displays the original, correct Chiikawa image for comparison. It should be scaled down slightly to fit its area with padding.Title Text: A celebratory title, "Your Masterpiece!", is displayed above the canvas.Action Buttons: Two buttons appear below the canvas:"Play Again": Resets the game."Share Creation": Exports the player's work.Both buttons must have hover and active states.Interaction Logic:The player's creation and the reference image should appear with a quick, satisfying animation (e.g., scale-up/pop-in over 250ms).Clicking "Play Again" resets the game state and all variables, returning the user to the START_SCREEN.Clicking "Share Creation" triggers a browser download of a PNG image named chiikawa-creation.png. This image must only contain the content of the Player Creation Area, not the entire canvas.3. Technical Specifications3.1. Technology StackRuntime: HTML5, CSS3, JavaScript (ES6+).Rendering Engine: PixiJS (v7 or higher), loaded via CDN (https://cdnjs.cloudflare.com/ajax/libs/pixi.js/7.4.2/pixi.min.js).3.2. Application & Canvas ConfigurationLogical Resolution: 800px (width) x 600px (height).Background Color: White (0xFFFFFF).Display: The application canvas and UI controls must be horizontally and vertically centered within the browser viewport.DPI Scaling: The PixiJS application should be configured with autoDensity: true or resolution: window.devicePixelRatio || 1 to ensure crisp rendering on high-DPI displays.3.3. Asset ManagementDirectory: All image assets must be located in an assets/images/ directory.Format: All assets must be high-quality, transparent background PNGs. They should all be exported from a single source file to ensure their relative scales are correct.Asset List:chiikawa_full.png (For MEMORY state and REVEAL state reference).chiikawa_head.pngchiikawa_body.pngchiikawa_pochette.pngchiikawa_weapon.pngAsset Loading: All required image assets must be preloaded using PIXI.Assets.load() before the START_SCREEN is interactable. A simple text element "Loading..." should be displayed during this process.3.4. Core Implementation Details (PixiJS)State Management: A top-level variable let gameState = 'LOADING'; will control the application flow.Scene Graph:app.stage: The root container.puzzleContainer: A PIXI.Container to hold the parts placed by the player. This is crucial for the "Share" functionality.uiContainer: A PIXI.Container for in-game UI elements like text.Part Data Structure: An array of objects must define the puzzle parts and their properties. The array order dictates the placement sequence.const puzzleParts = [
    { name: 'Head', textureUrl: 'assets/images/chiikawa_head.png' },
    { name: 'Body', textureUrl: 'assets/images/chiikawa_body.png' },
    { name: 'Pochette', textureUrl: 'assets/images/chiikawa_pochette.png' },
    { name: 'Weapon', textureUrl: 'assets/images/chiikawa_weapon.png' }
];
Share Functionality:This must be implemented by generating a texture from the puzzleContainer specifically.Use app.renderer.extract.canvas(puzzleContainer) to capture only the player's creation.Create a temporary <a> element in the DOM, set its href to the extracted canvas's data URL (.toDataURL('image/png')), set its download attribute to chiikawa-creation.png, and programmatically trigger a click.4. Non-Functional RequirementsPerformance: The application must maintain a stable 60 FPS on modern desktop browsers (Chrome, Firefox, Safari, Edge).Responsiveness: While the canvas is fixed-size, the overall page layout must be responsive, ensuring the game remains centered and usable on various desktop screen sizes.Browser Compatibility: The game must be fully functional and visually consistent on the latest versions of the aforementioned browsers.Accessibility: UI text and background colors should have sufficient contrast (e.g., WCAG AA).